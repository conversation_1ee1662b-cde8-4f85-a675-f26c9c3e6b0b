.photo_box{
  width: 750rpx;
  border: 1px solid #cccccc;
  box-sizing: border-box;
}
.photo_text{
  width: 100%;
  line-height: 500rpx;
  text-align: center;
}
.photo_preview image{
  width: 750rpx;
}
.photo_preview .reselect{
  width: 750rpx;
  height: 100rpx;
  background-color: #3F8EFF;
  text-align: center;
  line-height: 100rpx;
  border-top: 1px solid #cccccc;
}

text {
    display: block;
}

.title {
    margin-top: 10px;
    font-size: 16px;
    line-height: 32px;
    font-weight: bold;
}

.imgWrapper {
    margin: 10px 10px 0;
    white-space: nowrap;
}
.img {
    width: 960px;
    height: 960px;
    border: 1px solid #f1f1f1;
}

.result {
    margin-top: 5px;
}

.selected {
    border: 1px solid #999;
}

.select-btn {
    margin-top: 20px;
    width: 60%;
}

.mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, .7);
}

.loading {
    color: #fff;
    font-size: 20px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.img-view {
    padding-bottom: 20px;
    border-bottom: 1px solid #f1f1f1;
}
