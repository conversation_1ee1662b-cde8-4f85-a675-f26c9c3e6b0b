
if(NOT ${<PERSON><PERSON><PERSON><PERSON>_LITE_BACKEND})
    message(WARNING "While compiling with -DWITH_TIMVX=ON, will force to set -DENABLE_LITE_BACKEND=ON")
    set(ENABLE_LITE_BACKEND ON)
endif()
if(${ENABLE_PADDLE2ONNX})
    message(WARNING "While compiling with -DWITH_TIMVX=ON, will force to set -DENABLE_PADDLE2ONNX=OFF")
    set(ENABLE_PADDLE2ONNX OFF)
endif()
if(${ENABLE_ORT_BACKEND})
    message(WARNING "While compiling with -DWITH_TIMVX=ON, will force to set -DENABLE_ORT_BACKEND=OFF")
    set(ENABLE_ORT_BACKEND OFF)
endif()
if(${ENABLE_PADDLE_BACKEND})
    message(WARNING "While compiling with -DWITH_TIMVX=ON, will force to set -<PERSON><PERSON><PERSON>E_PADDLE_BACKEND=OFF")
    set(<PERSON><PERSON><PERSON><PERSON>_PADDLE_BACKEND OFF)
endif()
if(${<PERSON><PERSON><PERSON>E_OPENVINO_BACKEND})
    message(WARNING "While compiling with -DWITH_TIMVX=ON, will force to set -DENABLE_OPENVINO_BACKEND=OFF")
    set(ENABLE_OPENVINO_BACKEND OFF)
endif()
if(${ENABLE_TRT_BACKEND})
    message(WARNING "While compiling with -DWITH_TIMVX=ON, will force to set -DENABLE_TRT_BACKEND=OFF")
    set(ENABLE_TRT_BACKEND OFF)
endif()

if(${WITH_GPU})
    message(WARNING "While compiling with -DWITH_TIMVX=ON, will force to set -DWITH_GPU=OFF")
    set(WITH_GPU OFF)
endif()

if(${ENABLE_TEXT})
    set(ENABLE_TEXT OFF CACHE BOOL "Force ENABLE_TEXT OFF" FORCE)
    message(STATUS "While compiling with -DWITH_TIMVX=ON, will force to set -DENABLE_TEXT=OFF")
endif()

install(FILES ${PROJECT_SOURCE_DIR}/cmake/timvx.cmake DESTINATION ${CMAKE_INSTALL_PREFIX})
install(FILES ${PROJECT_SOURCE_DIR}/cmake/toolchain.cmake DESTINATION ${CMAKE_INSTALL_PREFIX})


