# Face Recognition(人脸识别)

## fastdeploy.vision.faceid.AdaFace

```{eval-rst}
.. autoclass:: fastdeploy.vision.faceid.AdaFace
    :members:
    :inherited-members:
```

## fastdeploy.vision.faceid.CosFace

```{eval-rst}
.. autoclass:: fastdeploy.vision.faceid.CosFace
    :members:
    :inherited-members:
```

## fastdeploy.vision.faceid.ArcFace

```{eval-rst}
.. autoclass:: fastdeploy.vision.faceid.ArcFace
    :members:
    :inherited-members:
```

## fastdeploy.vision.faceid.PartialFC

```{eval-rst}
.. autoclass:: fastdeploy.vision.faceid.PartialFC
    :members:
    :inherited-members:
```

## fastdeploy.vision.faceid.VPL

```{eval-rst}
.. autoclass:: fastdeploy.vision.faceid.VPL
    :members:
    :inherited-members:
```
