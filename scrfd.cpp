#include "scrfd.h"
#include <algorithm>
#include <cmath>
#include <iostream>
#include <fstream>
#include <numeric>
#include <cstring>

SCRFD::SCRFD(const std::string& model_path) : model_path_(model_path) {
    // Initialize default parameters
    use_kps = true;
    size = {640, 640};
    padding_value = {0.0, 0.0, 0.0};
    is_mini_pad = false;
    is_no_pad = false;
    is_scale_up = false;
    stride = 32;
    downsample_strides = {8, 16, 32};
    num_anchors = 2;
    landmarks_per_face = 5;
    center_points_is_update_ = false;
    max_nms = 30000;
    disable_normalize_ = false;
    disable_permute_ = false;
}

SCRFD::~SCRFD() {
    if (ctx_ > 0) {
        rknn_destroy(ctx_);
    }
    if (input_attrs_) {
        delete[] input_attrs_;
    }
    if (output_attrs_) {
        delete[] output_attrs_;
    }
}

bool SCRFD::Initialize() {
    // Load model file
    std::ifstream file(model_path_, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
        std::cerr << "Failed to open model file: " << model_path_ << std::endl;
        return false;
    }
    
    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<char> model_data(size);
    if (!file.read(model_data.data(), size)) {
        std::cerr << "Failed to read model file" << std::endl;
        return false;
    }
    file.close();
    
    // Initialize RKNN
    int ret = rknn_init(&ctx_, model_data.data(), size, 0, nullptr);
    if (ret < 0) {
        std::cerr << "rknn_init failed: " << ret << std::endl;
        return false;
    }
    
    // Get model input/output info
    ret = rknn_query(ctx_, RKNN_QUERY_IN_OUT_NUM, &io_num_, sizeof(io_num_));
    if (ret < 0) {
        std::cerr << "rknn_query io_num failed: " << ret << std::endl;
        return false;
    }
    
    // Get input attributes
    input_attrs_ = new rknn_tensor_attr[io_num_.n_input];
    memset(input_attrs_, 0, sizeof(rknn_tensor_attr) * io_num_.n_input);
    for (uint32_t i = 0; i < io_num_.n_input; i++) {
        input_attrs_[i].index = i;
        ret = rknn_query(ctx_, RKNN_QUERY_INPUT_ATTR, &(input_attrs_[i]), sizeof(rknn_tensor_attr));
        if (ret < 0) {
            std::cerr << "rknn_query input attr failed: " << ret << std::endl;
            return false;
        }
    }
    
    // Get output attributes
    output_attrs_ = new rknn_tensor_attr[io_num_.n_output];
    memset(output_attrs_, 0, sizeof(rknn_tensor_attr) * io_num_.n_output);
    for (uint32_t i = 0; i < io_num_.n_output; i++) {
        output_attrs_[i].index = i;
        ret = rknn_query(ctx_, RKNN_QUERY_OUTPUT_ATTR, &(output_attrs_[i]), sizeof(rknn_tensor_attr));
        if (ret < 0) {
            std::cerr << "rknn_query output attr failed: " << ret << std::endl;
            return false;
        }
    }
    
    // Check if input is dynamic
    is_dynamic_input_ = false;
    for (uint32_t i = 0; i < input_attrs_[0].n_dims; i++) {
        if (i >= 2 && input_attrs_[0].dims[i] <= 0) {
            is_dynamic_input_ = true;
            break;
        }
    }
    
    if (!is_dynamic_input_) {
        is_mini_pad = false;
    }
    
    initialized_ = true;

    // Debug output
    std::cout << "SCRFD initialized successfully:" << std::endl;
    std::cout << "  Input: " << io_num_.n_input << " tensors" << std::endl;
    std::cout << "  Output: " << io_num_.n_output << " tensors" << std::endl;
    std::cout << "  Input shape: [";
    for (uint32_t i = 0; i < input_attrs_[0].n_dims; i++) {
        std::cout << input_attrs_[0].dims[i];
        if (i < input_attrs_[0].n_dims - 1) std::cout << ", ";
    }
    std::cout << "]" << std::endl;
    std::cout << "  Dynamic input: " << (is_dynamic_input_ ? "yes" : "no") << std::endl;

    return true;
}

void SCRFD::DisableNormalize() {
    disable_normalize_ = true;
}

void SCRFD::DisablePermute() {
    disable_permute_ = true;
}

bool SCRFD::Predict(const cv::Mat& im, FaceDetectionResult* result,
                    float conf_threshold, float nms_iou_threshold) {
    if (!initialized_) {
        std::cerr << "Model not initialized" << std::endl;
        return false;
    }
    
    cv::Mat mat = im.clone();
    std::map<std::string, std::array<float, 2>> im_info;
    
    // Record original image shape
    im_info["input_shape"] = {static_cast<float>(mat.rows), static_cast<float>(mat.cols)};
    im_info["output_shape"] = {static_cast<float>(mat.rows), static_cast<float>(mat.cols)};
    
    // Preprocess
    if (!Preprocess(mat, &im_info)) {
        std::cerr << "Failed to preprocess input image" << std::endl;
        return false;
    }
    
    // Prepare input
    rknn_input inputs[1];
    memset(inputs, 0, sizeof(inputs));
    inputs[0].index = 0;
    inputs[0].type = RKNN_TENSOR_FLOAT32;  // Changed from UINT8 to FLOAT32
    inputs[0].size = input_data_.size();
    inputs[0].fmt = disable_permute_ ? RKNN_TENSOR_NHWC : RKNN_TENSOR_NCHW;  // Set format based on permute setting
    inputs[0].buf = input_data_.data();
    
    int ret = rknn_inputs_set(ctx_, io_num_.n_input, inputs);
    if (ret < 0) {
        std::cerr << "rknn_inputs_set failed: " << ret << std::endl;
        return false;
    }
    
    // Run inference
    ret = rknn_run(ctx_, nullptr);
    if (ret < 0) {
        std::cerr << "rknn_run failed: " << ret << std::endl;
        return false;
    }
    
    // Get outputs
    rknn_output outputs[io_num_.n_output];
    memset(outputs, 0, sizeof(outputs));
    for (uint32_t i = 0; i < io_num_.n_output; i++) {
        outputs[i].want_float = 1;
    }
    
    ret = rknn_outputs_get(ctx_, io_num_.n_output, outputs, nullptr);
    if (ret < 0) {
        std::cerr << "rknn_outputs_get failed: " << ret << std::endl;
        return false;
    }
    
    // Convert outputs to cv::Mat format for postprocessing
    std::vector<cv::Mat> infer_result;
    for (uint32_t i = 0; i < io_num_.n_output; i++) {
        // Calculate the actual number of elements for this output
        int total_elements = 1;
        for (uint32_t j = 0; j < output_attrs_[i].n_dims; j++) {
            total_elements *= output_attrs_[i].dims[j];
        }

        // Create cv::Mat with proper dimensions
        // For SCRFD, outputs are typically [batch, height*width*anchors] for scores/boxes
        // We'll flatten to 1D for easier processing
        cv::Mat output_mat(total_elements, 1, CV_32F, outputs[i].buf);
        infer_result.push_back(output_mat.clone());
    }
    
    // Postprocess
    bool success = Postprocess(infer_result, result, im_info, conf_threshold, nms_iou_threshold);
    
    // Release outputs
    rknn_outputs_release(ctx_, io_num_.n_output, outputs);
    
    return success;
}

void SCRFD::LetterBox(cv::Mat& mat, const std::vector<int>& size,
                      const std::vector<float>& color, bool _auto,
                      bool scale_fill, bool scale_up, int stride) {
    float scale = std::min(size[1] * 1.0f / mat.rows, size[0] * 1.0f / mat.cols);
    if (!scale_up) {
        scale = std::min(scale, 1.0f);
    }

    int resize_h = static_cast<int>(std::round(mat.rows * scale));
    int resize_w = static_cast<int>(std::round(mat.cols * scale));

    int pad_w = size[0] - resize_w;
    int pad_h = size[1] - resize_h;

    if (_auto) {
        pad_h = pad_h % stride;
        pad_w = pad_w % stride;
    } else if (scale_fill) {
        pad_h = 0;
        pad_w = 0;
        resize_h = size[1];
        resize_w = size[0];
    }

    if (resize_h != mat.rows || resize_w != mat.cols) {
        cv::resize(mat, mat, cv::Size(resize_w, resize_h));
    }

    if (pad_h > 0 || pad_w > 0) {
        float half_h = pad_h * 0.5f;
        int top = static_cast<int>(std::round(half_h - 0.1f));
        int bottom = static_cast<int>(std::round(half_h + 0.1f));
        float half_w = pad_w * 0.5f;
        int left = static_cast<int>(std::round(half_w - 0.1f));
        int right = static_cast<int>(std::round(half_w + 0.1f));

        cv::Scalar pad_color(color[0], color[1], color[2]);
        cv::copyMakeBorder(mat, mat, top, bottom, left, right, cv::BORDER_CONSTANT, pad_color);
    }
}

bool SCRFD::Preprocess(cv::Mat& mat, std::map<std::string, std::array<float, 2>>* im_info) {
    float ratio = std::min(size[1] * 1.0f / static_cast<float>(mat.rows),
                          size[0] * 1.0f / static_cast<float>(mat.cols));

#ifndef __ANDROID__
    // Resize if ratio is not close to 1.0
    if (std::fabs(ratio - 1.0f) > 1e-06) {
        int resize_h = static_cast<int>(mat.rows * ratio);
        int resize_w = static_cast<int>(mat.cols * ratio);
        cv::resize(mat, mat, cv::Size(resize_w, resize_h));
    }
#endif

    // LetterBox
    LetterBox(mat, size, padding_value, is_mini_pad, is_no_pad, is_scale_up, stride);

    // BGR2RGB
    cv::cvtColor(mat, mat, cv::COLOR_BGR2RGB);

    // Normalize
    if (!disable_normalize_) {
        mat.convertTo(mat, CV_32F);
        // SCRFD normalization: (pixel / 128.0) - (127.5 / 128.0)
        // This is equivalent to: (pixel - 127.5) / 128.0
        mat = (mat - 127.5) / 128.0;
    } else {
        // If normalization is disabled, still convert to float for consistency
        mat.convertTo(mat, CV_32F);
    }

    // HWC2CHW and prepare input data
    if (!disable_permute_) {
        std::vector<cv::Mat> channels;
        cv::split(mat, channels);

        // Calculate total size
        int total_size = mat.rows * mat.cols * mat.channels();
        input_data_.resize(total_size * sizeof(float));

        float* data_ptr = reinterpret_cast<float*>(input_data_.data());
        int channel_size = mat.rows * mat.cols;

        for (int c = 0; c < mat.channels(); ++c) {
            channels[c].convertTo(channels[c], CV_32F);
            memcpy(data_ptr + c * channel_size, channels[c].data, channel_size * sizeof(float));
        }
    } else {
        // Keep HWC format
        mat.convertTo(mat, CV_32F);
        int total_size = mat.rows * mat.cols * mat.channels() * sizeof(float);
        input_data_.resize(total_size);
        memcpy(input_data_.data(), mat.data, total_size);
    }

    // Record output shape
    (*im_info)["output_shape"] = {static_cast<float>(mat.rows), static_cast<float>(mat.cols)};

    // Debug output
    std::cout << "Preprocessing completed:" << std::endl;
    std::cout << "  Input image: " << (*im_info)["input_shape"][1] << "x" << (*im_info)["input_shape"][0] << std::endl;
    std::cout << "  Output image: " << mat.cols << "x" << mat.rows << std::endl;
    std::cout << "  Input data size: " << input_data_.size() << " bytes" << std::endl;
    std::cout << "  Normalize: " << (!disable_normalize_ ? "enabled" : "disabled") << std::endl;
    std::cout << "  Permute: " << (!disable_permute_ ? "enabled" : "disabled") << std::endl;

    return true;
}

void SCRFD::GeneratePoints() {
    if (center_points_is_update_ && !is_dynamic_input_) {
        return;
    }

    // Clear existing points
    center_points_.clear();

    // Generate points for each stride (8, 16, 32)
    for (auto local_stride : downsample_strides) {
        unsigned int num_grid_w = size[0] / local_stride;
        unsigned int num_grid_h = size[1] / local_stride;

        // y
        for (unsigned int i = 0; i < num_grid_h; ++i) {
            // x
            for (unsigned int j = 0; j < num_grid_w; ++j) {
                // num_anchors, col major
                for (unsigned int k = 0; k < num_anchors; ++k) {
                    SCRFDPoint point;
                    point.cx = static_cast<float>(j);
                    point.cy = static_cast<float>(i);
                    center_points_[local_stride].push_back(point);
                }
            }
        }
    }

    center_points_is_update_ = true;
}

void SCRFD::SortDetectionResult(FaceDetectionResult* result) {
    if (result->boxes.empty()) return;

    // Create indices and sort by score in descending order
    std::vector<size_t> indices(result->scores.size());
    std::iota(indices.begin(), indices.end(), 0);

    std::sort(indices.begin(), indices.end(), [&](size_t a, size_t b) {
        return result->scores[a] > result->scores[b];
    });

    // Reorder all vectors according to sorted indices
    std::vector<std::array<float, 4>> sorted_boxes;
    std::vector<float> sorted_scores;
    std::vector<std::array<float, 2>> sorted_landmarks;

    sorted_boxes.reserve(result->boxes.size());
    sorted_scores.reserve(result->scores.size());
    if (result->landmarks_per_face > 0) {
        sorted_landmarks.reserve(result->landmarks.size());
    }

    for (size_t idx : indices) {
        sorted_boxes.push_back(result->boxes[idx]);
        sorted_scores.push_back(result->scores[idx]);

        if (result->landmarks_per_face > 0) {
            for (int j = 0; j < result->landmarks_per_face; ++j) {
                sorted_landmarks.push_back(result->landmarks[idx * result->landmarks_per_face + j]);
            }
        }
    }

    result->boxes = std::move(sorted_boxes);
    result->scores = std::move(sorted_scores);
    result->landmarks = std::move(sorted_landmarks);
}

void SCRFD::NMS(FaceDetectionResult* result, float iou_threshold) {
    SortDetectionResult(result);

    std::vector<float> area_of_boxes(result->boxes.size());
    std::vector<int> suppressed(result->boxes.size(), 0);

    // Calculate areas
    for (size_t i = 0; i < result->boxes.size(); ++i) {
        area_of_boxes[i] = (result->boxes[i][2] - result->boxes[i][0]) *
                          (result->boxes[i][3] - result->boxes[i][1]);
    }

    // NMS
    for (size_t i = 0; i < result->boxes.size(); ++i) {
        if (suppressed[i] == 1) {
            continue;
        }
        for (size_t j = i + 1; j < result->boxes.size(); ++j) {
            if (suppressed[j] == 1) {
                continue;
            }
            float xmin = std::max(result->boxes[i][0], result->boxes[j][0]);
            float ymin = std::max(result->boxes[i][1], result->boxes[j][1]);
            float xmax = std::min(result->boxes[i][2], result->boxes[j][2]);
            float ymax = std::min(result->boxes[i][3], result->boxes[j][3]);
            float overlap_w = std::max(0.0f, xmax - xmin);
            float overlap_h = std::max(0.0f, ymax - ymin);
            float overlap_area = overlap_w * overlap_h;
            float overlap_ratio = overlap_area / (area_of_boxes[i] + area_of_boxes[j] - overlap_area);
            if (overlap_ratio > iou_threshold) {
                suppressed[j] = 1;
            }
        }
    }

    // Create backup and filter results
    FaceDetectionResult backup = *result;
    int landmarks_per_face = result->landmarks_per_face;

    result->Clear();
    result->landmarks_per_face = landmarks_per_face;
    result->Reserve(suppressed.size());

    for (size_t i = 0; i < suppressed.size(); ++i) {
        if (suppressed[i] == 1) {
            continue;
        }
        result->boxes.emplace_back(backup.boxes[i]);
        result->scores.push_back(backup.scores[i]);

        // landmarks (if have)
        if (result->landmarks_per_face > 0) {
            for (size_t j = 0; j < result->landmarks_per_face; ++j) {
                result->landmarks.emplace_back(backup.landmarks[i * result->landmarks_per_face + j]);
            }
        }
    }
}

bool SCRFD::Postprocess(const std::vector<cv::Mat>& infer_result,
                        FaceDetectionResult* result,
                        const std::map<std::string, std::array<float, 2>>& im_info,
                        float conf_threshold, float nms_iou_threshold) {
    // number of downsample_strides
    int fmc = downsample_strides.size();

    // scrfd has 6,9,10,15 output tensors
    if (!(infer_result.size() == 9 || infer_result.size() == 6 ||
          infer_result.size() == 10 || infer_result.size() == 15)) {
        std::cerr << "The default number of output tensor must be 6, 9, 10, or 15 according to scrfd." << std::endl;
        return false;
    }

    if (!(fmc == 3 || fmc == 5)) {
        std::cerr << "The fmc must be 3 or 5" << std::endl;
        return false;
    }

    int total_num_boxes = 0;
    // compute the reserve space.
    for (int f = 0; f < fmc; ++f) {
        // For SCRFD, each stride level has height*width*num_anchors predictions
        int stride_val = downsample_strides[f];
        int grid_h = size[1] / stride_val;
        int grid_w = size[0] / stride_val;
        total_num_boxes += grid_h * grid_w * num_anchors;
    }

    GeneratePoints();
    result->Clear();

    // Debug output
    std::cout << "Postprocessing started:" << std::endl;
    std::cout << "  Output tensors: " << infer_result.size() << std::endl;
    std::cout << "  Expected total boxes: " << total_num_boxes << std::endl;
    std::cout << "  Confidence threshold: " << conf_threshold << std::endl;

    // scale the boxes to the origin image shape
    auto iter_out = im_info.find("output_shape");
    auto iter_ipt = im_info.find("input_shape");
    if (iter_out == im_info.end() || iter_ipt == im_info.end()) {
        std::cerr << "Cannot find input_shape or output_shape from im_info." << std::endl;
        return false;
    }

    float out_h = iter_out->second[0];
    float out_w = iter_out->second[1];
    float ipt_h = iter_ipt->second[0];
    float ipt_w = iter_ipt->second[1];
    float scale = std::min(out_h / ipt_h, out_w / ipt_w);
    if (!is_scale_up) {
        scale = std::min(scale, 1.0f);
    }
    float pad_h = (out_h - ipt_h * scale) / 2.0f;
    float pad_w = (out_w - ipt_w * scale) / 2.0f;
    if (is_mini_pad) {
        pad_h = static_cast<float>(static_cast<int>(pad_h) % stride);
        pad_w = static_cast<float>(static_cast<int>(pad_w) % stride);
    }

    // must be setup landmarks_per_face before reserve
    if (use_kps) {
        result->landmarks_per_face = landmarks_per_face;
    } else {
        // force landmarks_per_face = 0, if use_kps has been set as 'false'.
        result->landmarks_per_face = 0;
    }

    result->Reserve(total_num_boxes);
    unsigned int count = 0;

    // loop each stride
    for (int f = 0; f < fmc; ++f) {
        const float* score_ptr = reinterpret_cast<const float*>(infer_result[f].data);
        const float* bbox_ptr = reinterpret_cast<const float*>(infer_result[f + fmc].data);

        // Calculate number of points for this stride level
        int current_stride = downsample_strides[f];
        int grid_h = size[1] / current_stride;
        int grid_w = size[0] / current_stride;
        const unsigned int num_points = grid_h * grid_w * num_anchors;

        auto& stride_points = center_points_[current_stride];

        // Debug output
        std::cout << "Processing stride " << current_stride << ", num_points: " << num_points
                  << ", grid: " << grid_w << "x" << grid_h << std::endl;

        // loop each anchor
        for (unsigned int i = 0; i < num_points; ++i) {
            const float cls_conf = score_ptr[i];
            if (cls_conf < conf_threshold) continue;  // filter

            // Debug: print first few detections
            if (count < 5) {
                std::cout << "Detection " << count << ": score=" << cls_conf
                          << " (threshold=" << conf_threshold << ")" << std::endl;
            }

            // Check if index is valid
            if (i >= stride_points.size()) {
                std::cerr << "Warning: anchor index " << i << " >= stride_points.size() "
                          << stride_points.size() << std::endl;
                continue;
            }

            auto& point = stride_points.at(i);
            const float cx = point.cx;  // cx
            const float cy = point.cy;  // cy

            // bbox
            const float* offsets = bbox_ptr + i * 4;
            float l = offsets[0];  // left
            float t = offsets[1];  // top
            float r = offsets[2];  // right
            float b = offsets[3];  // bottom

            float x1 = ((cx - l) * static_cast<float>(current_stride) - static_cast<float>(pad_w)) / scale;
            float y1 = ((cy - t) * static_cast<float>(current_stride) - static_cast<float>(pad_h)) / scale;
            float x2 = ((cx + r) * static_cast<float>(current_stride) - static_cast<float>(pad_w)) / scale;
            float y2 = ((cy + b) * static_cast<float>(current_stride) - static_cast<float>(pad_h)) / scale;

            result->boxes.emplace_back(std::array<float, 4>{x1, y1, x2, y2});
            result->scores.push_back(cls_conf);

            if (use_kps) {
                const float* landmarks_ptr = reinterpret_cast<const float*>(infer_result[f + 2 * fmc].data);
                // landmarks
                const float* kps_offsets = landmarks_ptr + i * (landmarks_per_face * 2);
                for (unsigned int j = 0; j < landmarks_per_face * 2; j += 2) {
                    float kps_l = kps_offsets[j];
                    float kps_t = kps_offsets[j + 1];
                    float kps_x = ((cx + kps_l) * static_cast<float>(current_stride) - static_cast<float>(pad_w)) / scale;
                    float kps_y = ((cy + kps_t) * static_cast<float>(current_stride) - static_cast<float>(pad_h)) / scale;
                    result->landmarks.emplace_back(std::array<float, 2>{kps_x, kps_y});
                }
            }
            count += 1;  // limit boxes for nms.
            if (count > max_nms) {
                break;
            }
        }
    }

    if (result->boxes.size() == 0) {
        return true;
    }

    NMS(result, nms_iou_threshold);

    // scale and clip box
    for (size_t i = 0; i < result->boxes.size(); ++i) {
        result->boxes[i][0] = std::max(result->boxes[i][0], 0.0f);
        result->boxes[i][1] = std::max(result->boxes[i][1], 0.0f);
        result->boxes[i][2] = std::max(result->boxes[i][2], 0.0f);
        result->boxes[i][3] = std::max(result->boxes[i][3], 0.0f);
        result->boxes[i][0] = std::min(result->boxes[i][0], ipt_w - 1.0f);
        result->boxes[i][1] = std::min(result->boxes[i][1], ipt_h - 1.0f);
        result->boxes[i][2] = std::min(result->boxes[i][2], ipt_w - 1.0f);
        result->boxes[i][3] = std::min(result->boxes[i][3], ipt_h - 1.0f);
    }

    // scale and clip landmarks
    if (use_kps) {
        for (size_t i = 0; i < result->landmarks.size(); ++i) {
            result->landmarks[i][0] = std::max(result->landmarks[i][0], 0.0f);
            result->landmarks[i][1] = std::max(result->landmarks[i][1], 0.0f);
            result->landmarks[i][0] = std::min(result->landmarks[i][0], ipt_w - 1.0f);
            result->landmarks[i][1] = std::min(result->landmarks[i][1], ipt_h - 1.0f);
        }
    }

    return true;
}
