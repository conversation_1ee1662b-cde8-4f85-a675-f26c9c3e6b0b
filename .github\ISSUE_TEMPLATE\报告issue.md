---
name: 报告issue
about: 反馈使用中遇到的问题
title: ''
labels: ''
assignees: ''

---

*********************************************
温馨提示：根据社区不完全统计，按照模板提问，可以加快回复和解决问题的速度
*********************************************

## 环境

- 【FastDeploy版本】： 说明具体的版本，如fastdeploy-linux-gpu-0.8.0
- 【编译命令】如果您是自行编译的FastDeploy，请说明您的编译方式（参数命令）
- 【系统平台】: Linux x64(Ubuntu 18.04) / Windows x64(Windows10) / Mac OSX arm(12.0) / Mac OSX intel(12.0)
- 【硬件】： 说明具体硬件型号，如 Nvidia GPU 3080TI， CUDA 11.2 CUDNN 8.3
- 【编译语言】： C++ / Python(3.7或3.8等）

## 问题日志及出现问题的操作流程
- 附上详细的问题日志有助于快速定位分析
- 【模型跑不同】
- - 先执行`examples`下的部署示例，包括使用examples提供的模型，确认是否可以正确执行
- - 如若`examples`下的代码可以运行，但自己的模型，或自己的代码不能运行
- - - 提供复现问题的 代码+模型+错误log，供工程师快速定位问题
- 【模型精度问题】
- - 先执行`examples`下的部署示例，包括使用examples提供的模型，确认是否可以正确执行
- - 如若`examples`下的代码可以运行，但自己的模型，或自己的代码不能运行
- - - 提供复现问题的 代码+模型+错误log，供工程师快速定位问题
- 【性能问题】描述清楚对比的方式
- - 注意性能测试，循环跑N次，取后80%的用时平均（模型启动时，刚开始受限于资源分配，速度会较慢）
- - FastDeploy的Predict包含模型本身之外的数据前后处理用时
- - - 提供复现问题的 代码+模型+错误log，供工程师快速定位问题

