# Matting(图像抠图)

## fastdeploy.vision.matting.MODNet

```{eval-rst}
.. autoclass:: fastdeploy.vision.matting.MODNet
    :members:
    :inherited-members:
```

## fastdeploy.vision.matting.PPMatting

```{eval-rst}
.. autoclass:: fastdeploy.vision.matting.PPMatting
    :members:
    :inherited-members:
```

## fastdeploy.vision.matting.RobustVideoMatting

```{eval-rst}
.. autoclass:: fastdeploy.vision.matting.RobustVideoMatting
    :members:
    :inherited-members:
```
