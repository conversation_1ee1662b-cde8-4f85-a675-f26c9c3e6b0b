{"name": "@paddle-js-models/ocrdet", "version": "4.1.1", "description": "", "main": "lib/index.js", "module": "lib/index.esm.js", "typings": "lib/index.d.js", "files": ["lib", "LICENSE", "CHANGELOG.md", "README.md", "README_cn.md"], "keywords": [], "author": "", "license": "ISC", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "scripts": {"dev": "yalc publish --push", "prepublish": "pnpm lint & pnpm test", "prepublishOnly": "pnpm build", "build": "gulp build", "lint": "eslint --ext .js,.ts src --fix", "api": "api-extractor run", "test": "jest --coverage --verbose -u", "changelog": "gulp changelog"}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/preset-env": "^7.19.0", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@microsoft/api-extractor": "^7.30.0", "@types/d3-polygon": "^3.0.0", "@types/fs-extra": "^9.0.13", "@types/gulp": "^4.0.9", "@types/jest": "^29.0.1", "@types/node": "^18.7.16", "@typescript-eslint/eslint-plugin": "^5.36.2", "@typescript-eslint/parser": "^5.36.2", "browserify": "^17.0.0", "chalk": "4.1.2", "commitlint": "^17.1.2", "conventional-changelog-cli": "^2.2.2", "eslint": "8.22.0", "eslint-plugin-jest": "^27.0.4", "fs-extra": "^10.1.0", "gulp": "^4.0.2", "gulp-clean": "^0.4.0", "gulp-typescript": "6.0.0-alpha.1", "gulp-uglify": "^3.0.2", "husky": "^8.0.1", "jest": "^29.0.3", "lint-staged": "^13.0.3", "rollup": "^2.79.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-eslint": "^7.0.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-string": "^3.0.0", "rollup-plugin-typescript2": "^0.34.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "tsify": "^5.0.4", "typescript": "^4.8.3", "vinyl-buffer": "^1.0.1", "vinyl-source-stream": "^2.0.0"}, "dependencies": {"@paddlejs-mediapipe/opencv": "^0.0.4", "@paddlejs/paddlejs-backend-webgl": "^1.2.9", "@paddlejs/paddlejs-core": "^2.2.0", "d3-polygon": "^3.0.1", "js-clipper": "^1.0.1", "number-precision": "^1.5.2"}}