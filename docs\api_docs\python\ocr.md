# OCR(文字识别)

## fastdeploy.vision.ocr.DBDetectorPreprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.DBDetectorPreprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.DBDetectorPostprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.DBDetectorPostprocessor
    :members:
    :inherited-members:
```


## fastdeploy.vision.ocr.DBDetector

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.DBDetector
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.ClassifierPreprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.ClassifierPreprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.ClassifierPostprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.ClassifierPostprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.Classifier

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.Classifier
    :members:
    :inherited-members:
```


## fastdeploy.vision.ocr.RecognizerPreprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.RecognizerPreprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.RecognizerPostprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.RecognizerPostprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.Recognizer

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.Recognizer
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.PPOCRv2

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.PPOCRv2
    :members:
    :inherited-members:
```

## fastdeploy.vision.ocr.PPOCRv3

```{eval-rst}
.. autoclass:: fastdeploy.vision.ocr.PPOCRv3
    :members:
    :inherited-members:
```
