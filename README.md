# SCRFD Standalone Implementation

This is a standalone implementation of the SCRFD (Sample and Computation Redistribution for Efficient Face Detection) class that only depends on RKNN, OpenCV, and standard C++ libraries.

## Features

- **Lightweight**: Only depends on RKNN, OpenCV, and standard C++ libraries
- **Complete**: Includes all preprocessing, inference, and postprocessing steps
- **Configurable**: Supports various model configurations and parameters
- **Face Detection**: Detects faces with bounding boxes and optional landmarks

## Files

- `scrfd.h` - Header file containing the SCRFD class declaration
- `scrfd.cpp` - Implementation file with all SCRFD functionality
- `example.cpp` - Example usage of the SCRFD class
- `CMakeLists_SCRFD.txt` - CMake configuration file for building

## Dependencies

1. **RKNN SDK**: Required for model inference on RKNN-compatible devices
2. **OpenCV**: Used for image processing operations
3. **Standard C++ Libraries**: For basic functionality

## Building

1. Install dependencies:
   - RKNN SDK (for your target platform)
   - OpenCV (version 3.0 or higher)

2. Configure CMake:
   ```bash
   mkdir build
   cd build
   cmake -DRKNN_SDK_PATH=/path/to/your/rknn/sdk -f ../CMakeLists_SCRFD.txt ..
   ```

3. Build:
   ```bash
   make
   ```

## Usage

### Basic Usage

```cpp
#include "scrfd.h"

// Create SCRFD instance
SCRFD detector("path/to/your/model.rknn");

// Initialize the model
if (!detector.Initialize()) {
    std::cerr << "Failed to initialize model" << std::endl;
    return -1;
}

// Load image
cv::Mat image = cv::imread("path/to/image.jpg");

// Perform detection
FaceDetectionResult result;
bool success = detector.Predict(image, &result, 0.5f, 0.4f);

// Process results
for (size_t i = 0; i < result.boxes.size(); ++i) {
    const auto& box = result.boxes[i];
    float score = result.scores[i];
    std::cout << "Face " << i << ": bbox=[" << box[0] << ", " << box[1] 
              << ", " << box[2] << ", " << box[3] << "], score=" << score << std::endl;
}
```

### Configuration Parameters

The SCRFD class provides several configurable parameters:

```cpp
SCRFD detector("model.rknn");

// Configure input size
detector.size = {640, 640};

// Configure detection parameters
detector.downsample_strides = {8, 16, 32};
detector.landmarks_per_face = 5;
detector.use_kps = true;

// Configure preprocessing
detector.DisableNormalize();  // Disable normalization if needed
detector.DisablePermute();    // Disable HWC2CHW if needed
```

## API Reference

### SCRFD Class

#### Constructor
- `SCRFD(const std::string& model_path)`: Create SCRFD instance with model path

#### Methods
- `bool Initialize()`: Initialize the model and RKNN context
- `bool Predict(const cv::Mat& im, FaceDetectionResult* result, float conf_threshold, float nms_iou_threshold)`: Perform face detection
- `void DisableNormalize()`: Disable normalization in preprocessing
- `void DisablePermute()`: Disable HWC2CHW permutation in preprocessing
- `std::string ModelName()`: Get model name

#### Configuration Parameters
- `size`: Target input size (default: {640, 640})
- `downsample_strides`: Downsample strides (default: {8, 16, 32})
- `landmarks_per_face`: Number of landmarks per face (default: 5)
- `use_kps`: Enable keypoint detection (default: true)
- `max_nms`: Maximum boxes for NMS (default: 30000)

### FaceDetectionResult Structure

```cpp
struct FaceDetectionResult {
    std::vector<std::array<float, 4>> boxes;        // [xmin, ymin, xmax, ymax]
    std::vector<std::array<float, 2>> landmarks;    // [x, y] landmarks
    std::vector<float> scores;                      // confidence scores
    int landmarks_per_face;                         // number of landmarks per face
    
    void Clear();                                   // Clear all results
    void Reserve(int size);                         // Reserve memory
};
```

## Example

Run the example program:

```bash
./scrfd_example model.rknn input_image.jpg
```

This will:
1. Load the RKNN model
2. Process the input image
3. Detect faces with bounding boxes and landmarks
4. Save the result image with annotations

## Notes

- The model file should be in RKNN format (.rknn)
- Input images are expected in BGR format (OpenCV default)
- The implementation supports both face detection and landmark detection
- NMS (Non-Maximum Suppression) is applied to filter overlapping detections
- Results are scaled back to the original image dimensions

## License

This implementation is based on the original SCRFD implementation from FastDeploy and follows the same Apache 2.0 license.
