# Image Classification(图像分类)

## fastdeploy.vision.classification.PaddleClasPreprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.classification.PaddleClasPreprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.classification.PaddleClasPostprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.classification.PaddleClasPostprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.classification.PaddleClasModel

```{eval-rst}
.. autoclass:: fastdeploy.vision.classification.PaddleClasModel
    :members:
    :inherited-members:
```

## fastdeploy.vision.classification.YOLOv5Cls

```{eval-rst}
.. autoclass:: fastdeploy.vision.classification.YOLOv5Cls
    :members:
    :inherited-members:
```

## fastdeploy.vision.classification.ResNet

```{eval-rst}
.. autoclass:: fastdeploy.vision.classification.ResNet
    :members:
    :inherited-members:
```
