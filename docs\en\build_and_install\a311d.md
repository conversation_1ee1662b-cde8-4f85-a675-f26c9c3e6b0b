English | [中文](../../cn/build_and_install/a311d.md)

# How to Build A311D Deployment Environment

FastDeploy supports AI deployment on Rockchip Soc based on Paddle Lite backend. For more detailed information, please refer to: [Paddle Lite Deployment Example](https://www.paddlepaddle.org.cn/lite/develop/demo_guides/verisilicon_timvx.html).

This document describes how to compile the Paddle Lite based C++ FastDeploy cross-compilation library.

The relevant compilation options are described as follows:  
|Compile Options|Default Values|Description|Remarks|  
|:---|:---|:---|:---|  
|ENABLE_LITE_BACKEND|OFF|It needs to be set to ON when compiling the A311D library| - |  
|WITH_TIMVX|OFF|It needs to be set to ON when compiling the A311D library| - |  
|TARGET_ABI|NONE|It needs to be set to arm64 when compiling the A311D library| - |  

For more compilation options, please refer to [Description of FastDeploy compilation options](./README.md)

## Cross-compilation environment construction

### Host Environment Requirements  
- os：Ubuntu == 16.04
- cmake： version >= 3.10.0  

### Building the compilation environment
You can enter the FastDeploy/tools/timvx directory and use the following command to install:
```bash
cd FastDeploy/tools/timvx
bash install.sh
```
You can also install it with the following commands:
```bash
 # 1. Install basic software
apt update
apt-get install -y --no-install-recommends \
  gcc g++ git make wget python unzip

# 2. Install arm gcc toolchains
apt-get install -y --no-install-recommends \
  g++-arm-linux-gnueabi gcc-arm-linux-gnueabi \
  g++-arm-linux-gnueabihf gcc-arm-linux-gnueabihf \
  gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# 3. Install cmake 3.10 or above
wget -c https://mms-res.cdn.bcebos.com/cmake-3.10.3-Linux-x86_64.tar.gz && \
  tar xzf cmake-3.10.3-Linux-x86_64.tar.gz && \
  mv cmake-3.10.3-Linux-x86_64 /opt/cmake-3.10 && \
  ln -s /opt/cmake-3.10/bin/cmake /usr/bin/cmake && \
  ln -s /opt/cmake-3.10/bin/ccmake /usr/bin/ccmake
```

## FastDeploy cross-compilation library compilation based on Paddle Lite
After setting up the cross-compilation environment, the compilation command is as follows:
```bash
# Download the latest source code
git clone https://github.com/PaddlePaddle/FastDeploy.git
cd FastDeploy  
mkdir build && cd build

# CMake configuration with A311D toolchain
cmake -DCMAKE_TOOLCHAIN_FILE=./../cmake/toolchain.cmake \
      -DWITH_TIMVX=ON  \
      -DTARGET_ABI=arm64 \
      -DENABLE_FLYCV=ON \ # Whether to enable FlyCV optimization
      -DCMAKE_INSTALL_PREFIX=fastdeploy-timvx \
      -DENABLE_VISION=ON \ # Whether to compile the vision module
      -Wno-dev ..

# Build FastDeploy A311D C++ SDK
make -j8
make install
```  
After the compilation is complete, the fastdeploy-timvx directory will be generated, indicating that the FastDeploy library based on Paddle Lite TIM-VX has been compiled.

## Prepare the Soc environment
Before deployment, ensure that the version of the driver galcore.so of the Verisilicon Linux Kernel NPU meets the requirements. Before deployment, please log in to the development board, and enter the following command through the command line to query the NPU driver version. The recommended version of the Rockchip driver is: *******
```bash
dmesg | grep Galcore
```  
If the current version does not comply with the above, please read the following content carefully to ensure that the underlying NPU driver environment is correct.

There are two ways to modify the current NPU driver version:
1. Manually replace the NPU driver version. (recommend)
2. flash the machine, and flash the firmware that meets the requirements of the NPU driver version.

### Manually replace the NPU driver version
1. Use the following command to download and decompress the Paddle Lite demo, which provides ready-made driver files
```bash
wget https://paddlelite-demo.bj.bcebos.com/devices/generic/PaddleLite-generic-demo.tar.gz
tar -xf PaddleLite-generic-demo.tar.gz
```
2. Use `uname -a` to check `Linux Kernel` version, it is determined to be version 4.19.111.
3. Upload `galcore.ko` under `PaddleLite-generic-demo/libs/PaddleLite/linux/arm64/lib/verisilicon_timvx/viv_sdk_6_4_4_3/lib/a311d/4.9.113` path to the development board.
4. Log in to the development board, enter `sudo rmmod galcore` on the command line to uninstall the original driver, and enter `sudo insmod galcore.ko` to load the uploaded device driver. (Whether sudo is needed depends on the actual situation of the development board. For some adb-linked devices, please adb root in advance). If this step fails, go to method 2.
5. Enter `dmesg | grep Galcore` in the development board to query the NPU driver version, and it is determined to be: *******

### flash
According to the specific development board model, ask the development board seller or the official website customer service for the firmware and flashing method corresponding to the ******* version of the NPU driver.

For more details, please refer to: [Paddle Lite prepares the device environment](https://www.paddlepaddle.org.cn/lite/develop/demo_guides/verisilicon_timvx.html#zhunbeishebeihuanjing)

## Deployment example based on FastDeploy on A311D
1. For deploying the PaddleClas classification model on A311D, please refer to: [C++ deployment example of PaddleClas classification model on A311D](../../../examples/vision/classification/paddleclas/a311d/README.md)

2. For deploying PPYOLOE detection model on A311D, please refer to: [C++ deployment example of PPYOLOE detection model on A311D](../../../examples/vision/detection/paddledetection/a311d/README.md)

3. For deploying YOLOv5 detection model on A311D, please refer to: [C++ Deployment Example of YOLOv5 Detection Model on A311D](../../../examples/vision/detection/yolov5/a311d/README.md)

4. For deploying PP-LiteSeg segmentation model on A311D, please refer to: [C++ Deployment Example of PP-LiteSeg Segmentation Model on A311D](../../../examples/vision/segmentation/paddleseg/a311d/README.md)
