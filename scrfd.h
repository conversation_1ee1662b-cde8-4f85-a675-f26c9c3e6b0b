#pragma once

#include <vector>
#include <array>
#include <string>
#include <unordered_map>
#include <map>
#include <opencv2/opencv.hpp>
#include <rknn_api.h>

// Face detection result structure
struct FaceDetectionResult {
    std::vector<std::array<float, 4>> boxes;        // [xmin, ymin, xmax, ymax]
    std::vector<std::array<float, 2>> landmarks;    // [x, y] landmarks
    std::vector<float> scores;                      // confidence scores
    int landmarks_per_face = 0;                     // number of landmarks per face
    
    void Clear() {
        boxes.clear();
        landmarks.clear();
        scores.clear();
        landmarks_per_face = 0;
    }
    
    void Reserve(int size) {
        boxes.reserve(size);
        scores.reserve(size);
        if (landmarks_per_face > 0) {
            landmarks.reserve(size * landmarks_per_face);
        }
    }
};

// SCRFD face detection class
class SCRFD {
public:
    /**
     * @brief Constructor
     * @param model_path Path to RKNN model file
     */
    explicit SCRFD(const std::string& model_path);
    
    /**
     * @brief Destructor
     */
    ~SCRFD();
    
    /**
     * @brief Initialize the model
     * @return true if successful, false otherwise
     */
    bool Initialize();
    
    /**
     * @brief Predict face detection result for an input image
     * @param im Input image (BGR format)
     * @param result Output face detection result
     * @param conf_threshold Confidence threshold for postprocessing (default: 0.25)
     * @param nms_iou_threshold IoU threshold for NMS (default: 0.4)
     * @return true if prediction succeeded, false otherwise
     */
    bool Predict(const cv::Mat& im, FaceDetectionResult* result,
                 float conf_threshold = 0.25f,
                 float nms_iou_threshold = 0.4f);
    
    /**
     * @brief Get model name
     * @return Model name string
     */
    std::string ModelName() const { return "scrfd"; }
    
    /**
     * @brief Disable normalize in preprocessing step
     */
    void DisableNormalize();
    
    /**
     * @brief Disable HWC2CHW permute in preprocessing step
     */
    void DisablePermute();
    
    // Configuration parameters
    std::vector<int> size = {640, 640};                    // Target size after resize
    std::vector<float> padding_value = {0.0, 0.0, 0.0};   // Padding value
    bool is_mini_pad = false;                              // Mini pad mode
    bool is_no_pad = false;                                // No pad mode
    bool is_scale_up = false;                              // Allow scale up
    int stride = 32;                                       // Padding stride
    std::vector<int> downsample_strides = {8, 16, 32};     // Downsample strides
    int landmarks_per_face = 5;                            // Landmarks per face
    bool use_kps = true;                                   // Use keypoints
    int max_nms = 30000;                                   // Max boxes for NMS
    unsigned int num_anchors = 2;                          // Anchor number per stride

private:
    // Internal structures
    struct SCRFDPoint {
        float cx;
        float cy;
    };
    
    // Private methods
    bool Preprocess(cv::Mat& mat, std::map<std::string, std::array<float, 2>>* im_info);
    bool Postprocess(const std::vector<cv::Mat>& infer_result, 
                     FaceDetectionResult* result,
                     const std::map<std::string, std::array<float, 2>>& im_info,
                     float conf_threshold, float nms_iou_threshold);
    void GeneratePoints();
    void LetterBox(cv::Mat& mat, const std::vector<int>& size,
                   const std::vector<float>& color, bool _auto,
                   bool scale_fill = false, bool scale_up = true,
                   int stride = 32);
    void NMS(FaceDetectionResult* result, float iou_threshold);
    void SortDetectionResult(FaceDetectionResult* result);
    
    // RKNN related
    rknn_context ctx_ = 0;
    rknn_input_output_num io_num_;
    rknn_tensor_attr* input_attrs_ = nullptr;
    rknn_tensor_attr* output_attrs_ = nullptr;
    
    // Model configuration
    std::string model_path_;
    bool initialized_ = false;
    bool is_dynamic_input_ = false;
    bool center_points_is_update_ = false;
    bool disable_normalize_ = false;
    bool disable_permute_ = false;
    
    // Center points cache
    std::unordered_map<int, std::vector<SCRFDPoint>> center_points_;
    
    // Input tensor buffer
    std::vector<uint8_t> input_data_;
};
