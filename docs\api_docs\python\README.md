# Python API

This directory help to generate Python API documents for FastDeploy.

1. First, to generate the latest api documents, you need to install the latest FastDeploy, refer [build and install](../../cn/build_and_install) to build FastDeploy python wheel package with the latest code.  
2. After installed FastDeploy in your python environment, there are some dependencies need to install, execute command `pip install -r requirements.txt` in this directory  
3. Execute command `make html` to generate API documents

After all the step done, use browser open `_build/html/index.html`.
