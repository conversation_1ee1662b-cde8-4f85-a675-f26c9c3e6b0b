[English](../../README_EN.md) | [简体中文](../../README_CN.md) | हिन्दी | [日本語](./README_日本語.md) | [한국인](./README_한국인.md) | [Pу́сский язы́к](./README_Pу́сский_язы́к.md)

![⚡️FastDeploy](https://user-images.githubusercontent.com/31974251/185771818-5d4423cd-c94c-4a49-9894-bc7a8d1c29d0.png)

</p>

<p align="center">
    <a href="./LICENSE"><img src="https://img.shields.io/badge/license-Apache%202-dfd.svg"></a>
    <a href="https://github.com/PaddlePaddle/FastDeploy/releases"><img src="https://img.shields.io/github/v/release/PaddlePaddle/FastDeploy?color=ffa"></a>
    <a href=""><img src="https://img.shields.io/badge/python-3.7+-aff.svg"></a>
    <a href=""><img src="https://img.shields.io/badge/os-linux%2C%20win%2C%20mac-pink.svg"></a>
    <a href="https://github.com/PaddlePaddle/FastDeploy/graphs/contributors"><img src="https://img.shields.io/github/contributors/PaddlePaddle/FastDeploy?color=9ea"></a>
    <a href="https://github.com/PaddlePaddle/FastDeploy/commits"><img src="https://img.shields.io/github/commit-activity/m/PaddlePaddle/FastDeploy?color=3af"></a>
    <a href="https://github.com/PaddlePaddle/FastDeploy/issues"><img src="https://img.shields.io/github/issues/PaddlePaddle/FastDeploy?color=9cc"></a>
    <a href="https://github.com/PaddlePaddle/FastDeploy/stargazers"><img src="https://img.shields.io/github/stars/PaddlePaddle/FastDeploy?color=ccf"></a>
</p>

<p align="center">
    <a href="./../../docs/cn/build_and_install"><b> संस्थापन  </b></a>
    |
    <a href="./../../docs/README_EN.md"><b> दस्तावेज़ीकरण का उपयोग करें </b></a>
    |
   <a href="./../../README_EN.md#Quick-Start"><b> तेजी से शुरू </b></a>
    |
    <a href="https://baidu-paddle.github.io/fastdeploy-api/"><b> APIप्रलेखन </b></a>
    |
    <a href="https://github.com/PaddlePaddle/FastDeploy/releases"><b> चेंजलॉग </b></a>
</p>

<div align="center">
    
[<img src='https://user-images.githubusercontent.com/54695910/200465949-da478e1b-21ce-43b8-9f3f-287460e786bd.png' height="80px" width="110px">](../../examples/vision/classification)
[<img src='https://user-images.githubusercontent.com/54695910/188054680-2f8d1952-c120-4b67-88fc-7d2d7d2378b4.gif' height="80px" width="110px">](../../examples/vision/detection)
[<img src='https://user-images.githubusercontent.com/54695910/188054711-6119f0e7-d741-43b1-b273-9493d103d49f.gif' height="80px" width="110px">](../../examples/vision/segmentation/paddleseg)
[<img src='https://user-images.githubusercontent.com/54695910/188054718-6395321c-8937-4fa0-881c-5b20deb92aaa.gif' height="80px" width="110px">](../../examples/vision/segmentation/paddleseg)
[<img src='https://user-images.githubusercontent.com/54695910/188058231-a5fe1ce1-0a38-460f-9582-e0b881514908.gif' height="80px" width="110px">](../../examples/vision/matting)
[<img src='https://user-images.githubusercontent.com/54695910/188054691-e4cb1a70-09fe-4691-bc62-5552d50bd853.gif' height="80px" width="110px">](../../examples/vision/matting)
[<img src='https://user-images.githubusercontent.com/54695910/188054669-a85996ba-f7f3-4646-ae1f-3b7e3e353e7d.gif' height="80px" width="110px">](../../examples/vision/ocr)<br>
[<img src='https://user-images.githubusercontent.com/54695910/188059460-9845e717-c30a-4252-bd80-b7f6d4cf30cb.png' height="80px" width="110px">](../../examples/vision/facealign)
[<img src='https://user-images.githubusercontent.com/54695910/188054671-394db8dd-537c-42b1-9d90-468d7ad1530e.gif' height="80px" width="110px">](../../examples/vision/keypointdetection)
[<img src='https://user-images.githubusercontent.com/48054808/173034825-623e4f78-22a5-4f14-9b83-dc47aa868478.gif' height="80px" width="110px">](https://github.com/PaddlePaddle/FastDeploy/issues/6)
[<img src='https://user-images.githubusercontent.com/54695910/200162475-f5d85d70-18fb-4930-8e7e-9ca065c1d618.gif' height="80px" width="110px">](../../examples/text)
[<img src='https://user-images.githubusercontent.com/54695910/212314909-77624bdd-1d12-4431-9cca-7a944ec705d3.png' height="80px" width="110px">](https://paddlespeech.bj.bcebos.com/Parakeet/docs/demos/parakeet_espnet_fs2_pwg_demo/tn_g2p/parakeet/001.wav)
</div>


**⚡️फास्टडिप्लोय**एक एआई अनुमान तैनाती उपकरण है जो **सभी परिदृश्य**, **उपयोग करने में आसान और लचीला** और **बेहद कुशल** है। एक📦**आउट-ऑफ-द-बॉक्स** **क्लाउड-एज** परिनियोजन अनुभव प्रदान करता  है, 🔥160+ से अधिक **टेक्स्ट**, **विजन**, **स्पीच** और **क्रॉस-मोडल** मॉडल का समर्थन करता है, और 🔚 **एंड-टू-एंड** अनुमान प्रदर्शन अनुकूलन को लागू करता है। डेवलपर्स की जरूरतों को पूरा करने के लिए छवि वर्गीकरण, ऑब्जेक्ट डिटेक्शन, छवि विभाजन, चेहरे का पता लगाने, चेहरे की पहचान, मुख्य बिंदु का पता लगाने, कटआउट, ओसीआर, एनएलपी, टीटीएस और अन्य कार्यों सहित **बहु-परिदृश्य, बहु-हार्डवेयर, बहु-मंच** उद्योग की तैनाती की जरूरत है।

<div align="center">
    
<img src="https://user-images.githubusercontent.com/115439700/212800436-9cb39830-fca5-4b40-9def-a1fd83fcfc90.png" >
    
</div>


## 🌠 ताज़ा अपडेट

- ✨✨✨ **2023.01.17** यह रिलीज फास्ट डिसेनियोजित हार्डवेयर श्रृंखला पर [**YOLOv8**](./../../examples/vision/detection/paddledetection/) की तैनाती का समर्थन करता है। जो भी शामिल [**Paddle YOLOv8**](https://github.com/PaddlePaddle/PaddleYOLO/tree/release/2.5/configs/yolov8) तथा [**समुदाय ultralytics YOLOv8**](https://github.com/ultralytics/ultralytics)
    - [**Paddle YOLOv8**](https://github.com/PaddlePaddle/PaddleYOLO/tree/release/2.5/configs/yolov8) हार्डवेयर निर्दिष्ट करता है जो तैनात किया जा सकता है।[**Intel CPU**](./../../examples/vision/detection/paddledetection/python/infer_yolov8.py)、[**NVIDIA GPU**](./../../examples/vision/detection/paddledetection/python/infer_yolov8.py)、[**Jetson**](./../../examples/vision/detection/paddledetection/python/infer_yolov8.py)、[**Phytium**](./../../examples/vision/detection/paddledetection/python/infer_yolov8.py)、[**KunlunXin**](./../../examples/vision/detection/paddledetection/python/infer_yolov8.py)、[**Huawei Ascend**](./../../examples/vision/detection/paddledetection/python/infer_yolov8.py)、[**ARM CPU**](./../../examples/vision/detection/paddledetection/cpp/infer_yolov8.cc), शामिल **Python** तैनाती तथा **C++** तैनाती；**गणना ऊर्जा** और **RK3588** अद्यतन किया जा रहा है।
    -  [**समुदाय ultralytics YOLOv8**](https://github.com/ultralytics/ultralytics)[**Intel CPU**] हार्डवेयर निर्दिष्ट करता है जो तैनात किया जा सकता है।(./examples/vision/detection/yolov8)、[**NVIDIA GPU**](./../../examples/vision/detection/yolov8)、[**Jetson**](./../../examples/vision/detection/yolov8), शामिल **Python** तैनाती तथा **C++** तैनाती；
    -  FastDeploy एक लाइन मॉडल API स्विचन **YOLOv8**、 **PP-YOLOE+**、**YOLOv5** और अन्य मॉडलों की प्रदर्शन तुलना का एहसास कर सकते हैं।
 
- **✨👥✨ सामुदायिक संचार**

    - **Slack**：Join our [Slack community](https://join.slack.com/t/fastdeployworkspace/shared_invite/zt-1m88mytoi-mBdMYcnTF~9LCKSOKXd6Tg) and chat with other community members about ideas

    - **WeChat**: दो आयामी कोड स्कैन करें, तकनीकी समुदाय में शामिल होने के लिए प्रश्नावली भरें और समुदाय डेवलपर्स के साथ तैनात उद्योग से होने वाले दर्द बिंदुओं के बारे में संवाद करें।

<div align="center">
    <img src="https://user-images.githubusercontent.com/54695910/200145290-d5565d18-6707-4a0b-a9af-85fd36d35d13.jpg" width = "150" height = "150" />
</div>


<div id="fastdeploy-acknowledge"></div>

## 🌌 तर्क वापस अंत और क्षमता

<font size=0.5em>

|  | वीडियो स्ट्रीम | सेवाकरण परिनियोजन |एंड टू एंड परफॉरमेंस ऑप्टिमाइज़ेशन।| Linux  | Windows  | Android  |macOS |
|:----------|:----------:|:----------:|:----------:|:----------:|:----------:|:----------:|:----------:|
| X86_64&nbsp;CPU  |     |&nbsp;&nbsp;&nbsp;<img src="https://user-images.githubusercontent.com/54695910/212545467-e64ee45d-bf12-492c-b263-b860cb1e172b.png" height = "25"/>&nbsp;&nbsp;&nbsp;    |   <img src="https://user-images.githubusercontent.com/54695910/212474104-d82f3545-04d4-4ddd-b240-ffac34d8a920.svg" height = "17"/>  | <img src="https://user-images.githubusercontent.com/54695910/212473391-92c9f289-a81a-4927-9f31-1ab3fa3c2971.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473392-9df374d4-5daa-4e2b-856b-6e50ff1e4282.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473190-fdf3cee2-5670-47b5-85e7-6853a8dd200a.svg" height = "17"/>    | <img src="https://user-images.githubusercontent.com/54695910/212473391-92c9f289-a81a-4927-9f31-1ab3fa3c2971.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473392-9df374d4-5daa-4e2b-856b-6e50ff1e4282.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473190-fdf3cee2-5670-47b5-85e7-6853a8dd200a.svg" height = "17"/>   |   | <img src="https://user-images.githubusercontent.com/54695910/212473391-92c9f289-a81a-4927-9f31-1ab3fa3c2971.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473392-9df374d4-5daa-4e2b-856b-6e50ff1e4282.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473190-fdf3cee2-5670-47b5-85e7-6853a8dd200a.svg" height = "17"/>   |
| NVDIA&nbsp;GPU    | <img src="https://user-images.githubusercontent.com/54695910/212545467-e64ee45d-bf12-492c-b263-b860cb1e172b.png" height = "25"/>    | <img src="https://user-images.githubusercontent.com/54695910/212545467-e64ee45d-bf12-492c-b263-b860cb1e172b.png" height = "25"/>    | <img src="https://user-images.githubusercontent.com/54695910/212474106-a297aa0d-9225-458e-b5b7-e31aec7cfa79.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212474104-d82f3545-04d4-4ddd-b240-ffac34d8a920.svg" height = "17"/>   | <img src="https://user-images.githubusercontent.com/54695910/212473390-cebf7880-7c47-407d-94ae-01784d6a23d1.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473556-d2ebb7cc-e72b-4b49-896b-83f95ae1fe63.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473190-fdf3cee2-5670-47b5-85e7-6853a8dd200a.svg" height = "17"/>    |<img src="https://user-images.githubusercontent.com/54695910/212473390-cebf7880-7c47-407d-94ae-01784d6a23d1.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473556-d2ebb7cc-e72b-4b49-896b-83f95ae1fe63.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473190-fdf3cee2-5670-47b5-85e7-6853a8dd200a.svg" height = "17"/>  |     |    |
|Phytium CPU  |    |     |  <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/>  | <img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473393-ae1958bd-ab7d-4863-94b9-32863e600ba1.svg" height = "17"/>   |    |    |   |
| KunlunXin XPU |    |    | <img src="https://user-images.githubusercontent.com/54695910/212474104-d82f3545-04d4-4ddd-b240-ffac34d8a920.svg" height = "17"/>    |<img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/>    |     |   |    |
| Huawei Ascend NPU |     |     | <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212474104-d82f3545-04d4-4ddd-b240-ffac34d8a920.svg" height = "17"/>| <img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/>    |   |     |    |
|Graphcore&nbsp;IPU   |    | <img src="https://user-images.githubusercontent.com/54695910/212545467-e64ee45d-bf12-492c-b263-b860cb1e172b.png" height = "25"/>    |    |  <img src="https://user-images.githubusercontent.com/54695910/212473391-92c9f289-a81a-4927-9f31-1ab3fa3c2971.svg" height = "17"/>  |    |    |  |
| Sophgo    |     |     |     | <img src="https://user-images.githubusercontent.com/54695910/212473382-e3e9063f-c298-4b61-ad35-a114aa6e6555.svg" height = "17"/>   |    |  |    |
|Intel graphics card  |     |     |     | <img src="https://user-images.githubusercontent.com/54695910/212473392-9df374d4-5daa-4e2b-856b-6e50ff1e4282.svg" height = "17"/>   |    |   | |
|Jetson  |<img src="https://user-images.githubusercontent.com/54695910/212545467-e64ee45d-bf12-492c-b263-b860cb1e172b.png" height = "25"/>   | <img src="https://user-images.githubusercontent.com/54695910/212545467-e64ee45d-bf12-492c-b263-b860cb1e172b.png" height = "25"/>   | <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212474106-a297aa0d-9225-458e-b5b7-e31aec7cfa79.svg" height = "17"/>   |  <img src="https://user-images.githubusercontent.com/54695910/212473390-cebf7880-7c47-407d-94ae-01784d6a23d1.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473556-d2ebb7cc-e72b-4b49-896b-83f95ae1fe63.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473190-fdf3cee2-5670-47b5-85e7-6853a8dd200a.svg" height = "17"/>     |    |   |   |
|ARM&nbsp;CPU |    |     | <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212474104-d82f3545-04d4-4ddd-b240-ffac34d8a920.svg" height = "17"/>| <img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/><br><img src="https://user-images.githubusercontent.com/54695910/212473393-ae1958bd-ab7d-4863-94b9-32863e600ba1.svg" height = "17"/>   |    | <img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/>  |  <img src="https://user-images.githubusercontent.com/54695910/212473393-ae1958bd-ab7d-4863-94b9-32863e600ba1.svg" height = "17"/>  |
|RK3588 etc. |   |    | <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/>    | <img src="https://user-images.githubusercontent.com/54695910/212473387-2559cc2a-024b-4452-806c-6105d8eb2339.svg" height = "17"/>  |    |    |    |
|RV1126 etc. |    |    | <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/>    | <img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/>    |     |     |    |
| Amlogic |   |     | <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/>    | <img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/>   |     |     |   |
|  NXP |   |     | <img src="https://user-images.githubusercontent.com/54695910/212474105-38051192-9a1c-4b24-8ad1-f842fb0bf39d.svg" height = "17"/>    |<img src="https://user-images.githubusercontent.com/54695910/212473389-8c341bbe-30d4-4a28-b50a-074be4e98ce6.svg" height = "17"/>   |     |    |    |

</font>


## 🔮 प्रलेखन ट्यूटोरियल
- [✴️ Python SDK तेजी से शुरू](#fastdeploy-quick-start-python)  
- [✴️ C++ SDK तेजी से शुरू](#fastdeploy-quick-start-cpp)
- **संस्थापन दस्तावेज़**
    - [प्रीकम्पाइल लाइब्रेरी को डाउनलोड और इंस्टॉल करें](./../../docs/en/build_and_install/download_prebuilt_libraries.md)
    - [GPU संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/gpu.md)
    - [CPU संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/cpu.md)
    - [IPU संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/ipu.md)
    - [KunlunXin XPU संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/kunlunxin.md)
    - [Rockchip RV1126 संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/rv1126.md)
    - [Rockchip RK3588 संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/rknpu2.md)
    - [Amlogic A311D संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/a311d.md)
    - [Huawei Ascend संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/huawei_ascend.md)
    - [Jetson संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/jetson.md)
    - [Android संकलन और तैनाती पर्यावरण स्थापित करें](./../../docs/en/build_and_install/android.md)
- **त्वरित उपयोग**
    - [PP-YOLOE Python तैनाती उदाहरण](./../../docs/en/quick_start/models/python.md)
    - [PP-YOLOE C++ तैनाती उदाहरण](./../../docs/en/quick_start/models/cpp.md)
- **पीछे के अंत का उपयोग करें**
    - [Runtime Python उदाहरण का उपयोग करें](./../../docs/en/quick_start/runtime/python.md)
    - [Runtime C++ उदाहरण का उपयोग करें](./../../docs/en/quick_start/runtime/cpp.md)
    - [मॉडल परिनियोजन के लिए तर्क संबंधी अंत को मैं कैसे कॉन्फ़िगर करूं](./../../docs/en/faq/how_to_change_backend.md)
- **सेवाकरण परिनियोजन**
    - [सेवा तैनाती छवि संकलन और स्थापना](./../../serving/docs/EN/compile-en.md)
    - [सेवाकरण परिनियोजन](./../../serving)
- **API दस्तावेज़**
    - [Python API दस्तावेज़](https://www.paddlepaddle.org.cn/fastdeploy-api-doc/python/html/)
    - [C++ API दस्तावेज़](https://www.paddlepaddle.org.cn/fastdeploy-api-doc/cpp/html/)
    - [Android Java API दस्तावेज़](./../../java/android)
- **परफ़ॉर्मेंस ट्यूनिंग**
    - [त्वरण त्वरण](./../../docs/en/quantize.md)
    - [मल्टी थ्रेडेड मल्टी प्रोसेस उपयोग](./../../tutorials/multi_thread)
- **क्यू एंड ए**
    - [1. Windows कै से इस्तेमाल करे C++ SDK](./../../docs/en/faq/use_sdk_on_windows.md)
    - [2. Android कैसे इस्तेमाल करे FastDeploy C++ SDK](./../../docs/en/faq/use_cpp_sdk_on_android.md)
    - [3. TensorRT उपयोग की जा रही कुछ तकनीकें](./../../docs/en/faq/tensorrt_tricks.md)
- **अधिक Fastअधिक Deploy परिनियोजन मॉड्यूल**
    - [Benchmark परीक्षण](./../../benchmark)
- **मॉडल समर्थन सूची**
    - [🖥️ सर्वर साइड मॉडल सूचियों का समर्थन करता है](#fastdeploy-server-models)
    - [📳 मोबाइल और एंड-साइड मॉडल समर्थन सूची](#fastdeploy-edge-models)
    - [⚛️ Web और छोटे कार्यक्रम मॉडल समर्थन सूची](#fastdeploy-web-models)
- **💕डेवलपर योगदान**
    - [नया मॉडल जोड़ें](./../../docs/en/faq/develop_a_new_model.md)



<div id="fastdeploy-quick-start-python"></div>

## तेजी से शुरू💨

<details Open>

<summary><b>Python SDK तेजी से (खोलें और सिकोड़ें)b></summary><div>

### 🎆 त्वरित स्थापना

#### 🔸 पूर्वता

- CUDA >= 11.2、cuDNN >= 8.0、Python >= 3.6
- OS: Linux x86_64/macOS/Windows 10

#### 🔸 इंस्टॉल करें GPU संस्करण

```bash
pip install numpy opencv-python fastdeploy-gpu-python -f https://www.paddlepaddle.org.cn/whl/fastdeploy.html
```

#### [🔸 Conda स्थापना (अनुशंसित✨)](./../../docs/en/build_and_install/download_prebuilt_libraries.md)

```bash
conda config --add channels conda-forge && conda install cudatoolkit=11.2 cudnn=8.2
```

#### 🔸 इंस्टॉल करें CPU संस्करण

```bash
pip install numpy opencv-python fastdeploy-python -f https://www.paddlepaddle.org.cn/whl/fastdeploy.html
```

### 🎇 Python अनुमान उदाहरण

* मॉडल और तस्वीरें तैयार करें

```bash
wget https://bj.bcebos.com/paddlehub/fastdeploy/ppyoloe_crn_l_300e_coco.tgz
tar xvf ppyoloe_crn_l_300e_coco.tgz
wget https://gitee.com/paddlepaddle/PaddleDetection/raw/release/2.4/demo/000000014439.jpg
```

* परीक्षण अनुमान परिणाम

```python
# GPU/TensorRT तैनाती, संदर्भ examples/vision/detection/paddledetection/python
import cv2
import fastdeploy.vision as vision

model = vision.detection.PPYOLOE("ppyoloe_crn_l_300e_coco/model.pdmodel",
                                 "ppyoloe_crn_l_300e_coco/model.pdiparams",
                                 "ppyoloe_crn_l_300e_coco/infer_cfg.yml")
im = cv2.imread("000000014439.jpg")
result = model.predict(im)
print(result)

vis_im = vision.vis_detection(im, result, score_threshold=0.5)
cv2.imwrite("vis_image.jpg", vis_im)

```

</div></details>

<div id="fastdeploy-quick-start-cpp"></div>

<details close>

<summary><b>C++ SDK तेजी से शुरू (अधिक जानकारी के लिए क्लिक करें)</b></summary><div>


### 🎆 इंस्टॉल करें

-  संदर्भ [C++ प्रीकम्पाइल लाइब्रेरी डाउनलोड](./../../docs/cn/build_and_install/download_prebuilt_libraries.md)文档  

#### 🎇 C++ अनुमान उदाहरण

* मॉडल और तस्वीरें तैयार करें

```bash
wget https://bj.bcebos.com/paddlehub/fastdeploy/ppyoloe_crn_l_300e_coco.tgz
tar xvf ppyoloe_crn_l_300e_coco.tgz
wget https://gitee.com/paddlepaddle/PaddleDetection/raw/release/2.4/demo/000000014439.jpg
```

* परीक्षण अनुमान परिणाम

```C++
// GPU/TensorRT तैनाती, संदर्भ examples/vision/detection/paddledetection/cpp
#include "fastdeploy/vision.h"

int main(int argc, char* argv[]) {
  namespace vision = fastdeploy::vision;
  auto model = vision::detection::PPYOLOE("ppyoloe_crn_l_300e_coco/model.pdmodel",
                                          "ppyoloe_crn_l_300e_coco/model.pdiparams",
                                          "ppyoloe_crn_l_300e_coco/infer_cfg.yml");
  auto im = cv::imread("000000014439.jpg");

  vision::DetectionResult res;
  model.Predict(im, &res);

  auto vis_im = vision::VisDetection(im, res, 0.5);
  cv::imwrite("vis_image.jpg", vis_im);
  return 0;
}
```

</div></details>

अधिक तैनाती के मामलों के लिए, देखें[मॉडल परिनियोजन उदाहरण](./../../examples) .


<div id="fastdeploy-server-models"></div>


## ✴️ ✴️ सर्वर साइड मॉडल सूचियों का समर्थन करता है ✴️ ✴️ 

प्रतीक विवरण: (1)  ✅: पहले से समर्थित; (2) ❔:गति पर ; (3) N/A: समर्थित नहीं; <br>

<details open><summary><b> सर्वर-साइड मॉडल समर्थन सूची (ढहने के लिए क्लिक करें)</b></summary><div>

<div align="center">
  <img src="https://user-images.githubusercontent.com/54695910/198620704-741523c1-dec7-44e5-9f2b-29ddd9997344.png"/>
</div>

| कार्य परिदृश्य                  | नमूना                                                                                            | Linux                                            | Linux      | Win     | Win        | Mac     | Mac     | Linux       | Linux           | Linux         | Linux         | Linux   |
|:----------------------:|:--------------------------------------------------------------------------------------------:|:------------------------------------------------:|:----------:|:-------:|:----------:|:-------:|:-------:|:-----------:|:---------------:|:-------------:|:-------------:|:-------:|
| ---                    | ---                                                                                          | X86 CPU                                          | NVIDIA GPU | X86 CPU | NVIDIA GPU | X86 CPU | Arm CPU | AArch64 CPU |  Phytium D2000CPU | NVIDIA Jetson | Graphcore IPU | Serving |
| Classification         | [PaddleClas/ResNet50](./../../examples/vision/classification/paddleclas)                           | [✅](./../../examples/vision/classification/paddleclas) | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [TorchVison/ResNet](./../../examples/vision/classification/resnet)                                   | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Classification         | [ultralytics/YOLOv5Cls](./../../examples/vision/classification/yolov5cls)                             | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Classification         | [PaddleClas/PP-LCNet](./../../examples/vision/classification/paddleclas)                           | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/PP-LCNetv2](./../../examples/vision/classification/paddleclas)                         | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/EfficientNet](./../../examples/vision/classification/paddleclas)                       | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/GhostNet](./../../examples/vision/classification/paddleclas)                           | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/MobileNetV1](./../../examples/vision/classification/paddleclas)                        | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/MobileNetV2](./../../examples/vision/classification/paddleclas)                        | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/MobileNetV3](./../../examples/vision/classification/paddleclas)                        | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/ShuffleNetV2](./../../examples/vision/classification/paddleclas)                       | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/SqueeezeNetV1.1](./../../examples/vision/classification/paddleclas)                    | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Classification         | [PaddleClas/Inceptionv3](./../../examples/vision/classification/paddleclas)                        | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Classification         | [PaddleClas/PP-HGNet](./../../examples/vision/classification/paddleclas)                           | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ✅                           | ✅       |
| Detection              | [PaddleDetection/PP-YOLOE](./../../examples/vision/detection/paddledetection)                      | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [🔥PaddleDetection/YOLOv8](./../../examples/vision/detection/paddledetection)                      | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |✅       | ❔      |
| Detection              | [🔥ultralytics/YOLOv8](./../../examples/vision/detection/yolov8)                      | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔               | ✅                           | ❔                           | ❔      |❔       | ❔      |
| Detection              | [PaddleDetection/PicoDet](./../../examples/vision/detection/paddledetection)                       | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [PaddleDetection/YOLOX](./../../examples/vision/detection/paddledetection)                         | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅               | ✅                           | ❔                           | ✅       |
| Detection              | [PaddleDetection/YOLOv3](./../../examples/vision/detection/paddledetection)                        | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [PaddleDetection/PP-YOLO](./../../examples/vision/detection/paddledetection)                       | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [PaddleDetection/PP-YOLOv2](./../../examples/vision/detection/paddledetection)                     | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [PaddleDetection/Faster-RCNN](./../../examples/vision/detection/paddledetection)                   | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [PaddleDetection/Mask-RCNN](./../../examples/vision/detection/paddledetection)                     | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [Megvii-BaseDetection/YOLOX](./../../examples/vision/detection/yolox)                              | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Detection              | [WongKinYiu/YOLOv7](./../../examples/vision/detection/yolov7)                                      | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Detection              | [WongKinYiu/YOLOv7end2end_trt](./../../examples/vision/detection/yolov7end2end_trt)                | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Detection              | [WongKinYiu/YOLOv7end2end_ort_](./../../examples/vision/detection/yolov7end2end_ort)               | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Detection              | [meituan/YOLOv6](./../../examples/vision/detection/yolov6)                                         | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Detection              | [ultralytics/YOLOv5](./../../examples/vision/detection/yolov5)                                     | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Detection              | [WongKinYiu/YOLOR](./../../examples/vision/detection/yolor)                                        | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Detection              | [WongKinYiu/ScaledYOLOv4](./../../examples/vision/detection/scaledyolov4)                          | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Detection              | [ppogg/YOLOv5Lite](./../../examples/vision/detection/yolov5lite)                                   | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Detection              | [RangiLyu/NanoDetPlus](./../../examples/vision/detection/nanodet_plus)                             | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| KeyPoint               | [PaddleDetection/TinyPose](./../../examples/vision/keypointdetection/tiny_pose)                    | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| KeyPoint               | [PaddleDetection/PicoDet + TinyPose](./../../examples/vision/keypointdetection/det_keypoint_unite) | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| HeadPose               | [omasaht/headpose](./../../examples/vision/headpose)                                                 | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Tracking               | [PaddleDetection/PP-Tracking](./../../examples/vision/tracking/pptracking)                           | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| OCR                    | [PaddleOCR/PP-OCRv2](./../../examples/vision/ocr)                                                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| OCR                    | [PaddleOCR/PP-OCRv3](./../../examples/vision/ocr)                                                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ✅       |
| Segmentation           | [PaddleSeg/PP-LiteSeg](./../../examples/vision/segmentation/paddleseg)                             | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Segmentation           | [PaddleSeg/PP-HumanSegLite](./../../examples/vision/segmentation/paddleseg)                        | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Segmentation           | [PaddleSeg/HRNet](./../../examples/vision/segmentation/paddleseg)                                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Segmentation           | [PaddleSeg/PP-HumanSegServer](./../../examples/vision/segmentation/paddleseg)                      | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Segmentation           | [PaddleSeg/Unet](./../../examples/vision/segmentation/paddleseg)                                   | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Segmentation           | [PaddleSeg/Deeplabv3](./../../examples/vision/segmentation/paddleseg)                              | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| FaceDetection          | [biubug6/RetinaFace](./../../examples/vision/facedet/retinaface)                                   | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceDetection          | [Linzaer/UltraFace](./../../examples/vision/facedet/ultraface)                                     | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceDetection          | [deepcam-cn/YOLOv5Face](./../../examples/vision/facedet/yolov5face)                                | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceDetection          | [insightface/SCRFD](./../../examples/vision/facedet/scrfd)                                         | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceAlign              | [Hsintao/PFLD](./../../examples/vision/facealign/pfld)                                               | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceAlign              | [Single430FaceLandmark1000](./../../examples/vision/facealign/face_landmark_1000)                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| FaceAlign              | [jhb86253817/PIPNet](./../../examples/vision/facealign)                                            | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| FaceRecognition        | [insightface/ArcFace](./../../examples/vision/faceid/insightface)                                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceRecognition        | [insightface/CosFace](./../../examples/vision/faceid/insightface)                                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceRecognition        | [insightface/PartialFC](./../../examples/vision/faceid/insightface)                                | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| FaceRecognition        | [insightface/VPL](./../../examples/vision/faceid/insightface)                                      | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Matting                | [ZHKKKe/MODNet](./../../examples/vision/matting/modnet)                                            | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Matting                | [PeterL1n/RobustVideoMatting]()                                                              | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Matting                | [PaddleSeg/PP-Matting](./../../examples/vision/matting/ppmatting)                                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Matting                | [PaddleSeg/PP-HumanMatting](./../../examples/vision/matting/modnet)                                | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Matting                | [PaddleSeg/ModNet](./../../examples/vision/matting/modnet)                                         | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ✅                | ✅                           | ❔                           | ❔       |
| Video Super-Resolution | [PaddleGAN/BasicVSR](./)                                                                     | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Video Super-Resolution | [PaddleGAN/EDVR](./../../examples/vision/sr/edvr)                                                  | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Video Super-Resolution | [PaddleGAN/PP-MSVSR](./../../examples/vision/sr/ppmsvsr)                                           | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           | ❔       |
| Information Extraction | [PaddleNLP/UIE](./../../examples/text/uie)                                                         | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ✅                           | ❔                           |         |
| NLP                    | [PaddleNLP/ERNIE-3.0](./../../examples/text/ernie-3.0)                                             | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ❔                           | ❔                           | ✅       |
| Speech                 | [PaddleSpeech/PP-TTS](./../../examples/audio/pp-tts)                                                   | ✅                                                | ✅                        | ✅                        | ✅                        | ✅                       | ✅                     | ✅                          | ❔                | ❔                           | --                          | ✅       |

</div></details>

<div id="fastdeploy-edge-models"></div>

## 📲 मोबाइल और एंड-साइड मॉडल समर्थन सूची

<details open><summary><b>  एंड-साइड मॉडल समर्थन सूची (पतन के लिए क्लिक करें)</b></summary><div>

<div align="center">
  <img src="https://user-images.githubusercontent.com/54695910/198620704-741523c1-dec7-44e5-9f2b-29ddd9997344.png"  />
</div>

| कार्य परिदृश्य              | नमूना                                                                                           | आकार(MB)   | Linux   | Android | Linux     | Linux                   | Linux                          | Linux                       | Linux                            | TBD...  |
|:------------------:|:-----------------------------------------------------------------------------------------:|:--------:|:-------:|:-------:|:-------:|:-----------------------:|:------------------------------:|:---------------------------:|:--------------------------------:|:-------:|
| ---                | ---                                                                                       | ---      | ARM CPU | ARM CPU | Rockchip-NPU<br>RK3568/RK3588 | Rockchip-NPU<br>RV1109/RV1126/RK1808 |  Amlogic-NPU <br>A311D/S905D/C308X |  NXP-NPU<br>i.MX&nbsp;8M&nbsp;Plus | TBD...｜ |
| Classification     | [PaddleClas/ResNet50](./../../examples/vision/classification/paddleclas)                         | 98        | ✅       | ✅       |  ❔                             |      ✅                                |                                   |                                   |         |
| Classification     | [PaddleClas/PP-LCNet](./../../examples/vision/classification/paddleclas)                         | 11.9      | ✅       | ✅       | ❔                             | ✅                                 | --                                | --                                | --      |
| Classification     | [PaddleClas/PP-LCNetv2](./../../examples/vision/classification/paddleclas)                       | 26.6      | ✅       | ✅       | ❔                             | ✅                                   | --                                | --                                | --      |
| Classification     | [PaddleClas/EfficientNet](./../../examples/vision/classification/paddleclas)                     | 31.4      | ✅       | ✅       | ❔                             | ✅                                   | --                                | --                                | --      |
| Classification     | [PaddleClas/GhostNet](./../../examples/vision/classification/paddleclas)                         | 20.8      | ✅       | ✅       | ❔                             | ✅                                  | --                                | --                                | --      |
| Classification     | [PaddleClas/MobileNetV1](./../../examples/vision/classification/paddleclas)                      | 17        | ✅       | ✅       | ❔                             | ✅                                  | --                                | --                                | --      |
| Classification     | [PaddleClas/MobileNetV2](./../../examples/vision/classification/paddleclas)                      | 14.2      | ✅       | ✅       | ❔                             | ✅                                  | --                                | --                                | --      |
| Classification     | [PaddleClas/MobileNetV3](./../../examples/vision/classification/paddleclas)                      | 22        | ✅       | ✅       | ❔                             | ✅                                    | ❔                                 | ❔                                 | --      |
| Classification     | [PaddleClas/ShuffleNetV2](./../../examples/vision/classification/paddleclas)                     | 9.2       | ✅       | ✅       | ❔                             | ✅                                   | --                                | --                                | --      |
| Classification     | [PaddleClas/SqueezeNetV1.1](./../../examples/vision/classification/paddleclas)                   | 5         | ✅       | ✅       | ❔                             | ✅                                   | --                                | --                                | --      |
| Classification     | [PaddleClas/Inceptionv3](./../../examples/vision/classification/paddleclas)                      | 95.5      | ✅       | ✅       | ❔                             | ✅                                   | --                                | --                                | --      |
| Classification     | [PaddleClas/PP-HGNet](./../../examples/vision/classification/paddleclas)                         | 59        | ✅       | ✅       | ❔                             | ✅                                   | --                                | --                                | --      |
| Detection          | [PaddleDetection/PicoDet_s](./../../examples/vision/detection/paddledetection) | 4.9       | ✅       | ✅       | ✅                             | ✅                                    | ✅                                 | ✅                                 | --      |
| Detection          | [YOLOv5](./../../examples/vision/detection/rkyolo) |        |  ❔     |  ❔      | [✅](./../../examples/vision/detection/rkyolo)                             | ❔                                   | ❔                                 | ❔                                 | --      |
| Face Detection     | [deepinsight/SCRFD](./../../examples/vision/facedet/scrfd)                                     | 2.5       | ✅       | ✅       | ✅                             | --                                   | --                                | --                                | --      |
| Keypoint Detection | [PaddleDetection/PP-TinyPose](./../../examples/vision/keypointdetection/tiny_pose)               | 5.5       | ✅       | ✅       | ❔                             | ❔                                    | ❔                                 | ❔                                 | --      |
| Segmentation       | [PaddleSeg/PP-LiteSeg(STDC1)](./../../examples/vision/segmentation/paddleseg)                    | 32.2      | ✅       | ✅       | ✅                             | --                                   | --                                | --                                | --      |
| Segmentation       | [PaddleSeg/PP-HumanSeg-Lite](./../../examples/vision/segmentation/paddleseg)                     | 0.556     | ✅       | ✅       | ✅                             | --                                   | --                                | --                                | --      |
| Segmentation       | [PaddleSeg/HRNet-w18](./../../examples/vision/segmentation/paddleseg)                            | 38.7      | ✅       | ✅       | ✅                             | --                                   | --                                | --                                | --      |
| Segmentation       | [PaddleSeg/PP-HumanSeg](./../../examples/vision/segmentation/paddleseg)                          | 107.2     | ✅       | ✅       | ✅                             | --                                   | --                                | --                                | --      |
| Segmentation       | [PaddleSeg/Unet](./../../examples/vision/segmentation/paddleseg)                                 | 53.7      | ✅       | ✅       | ✅                             | --                                   | --                                | --                                | --      |
| Segmentation       | [PaddleSeg/Deeplabv3](./../../examples/vision/segmentation/paddleseg)                            | 150       | ❔       | ✅       | ✅                             |                                      |                                   |                                   |         |
| OCR                | [PaddleOCR/PP-OCRv2](./../../examples/vision/ocr/PP-OCRv2)                                       | 2.3+4.4   | ✅       | ✅       | ❔                             | --                                   | --                                | --                                | --      |
| OCR                | [PaddleOCR/PP-OCRv3](./../../examples/vision/ocr/PP-OCRv3)                                       | 2.4+10.6  | ✅       | ❔       | ❔                             | ❔                                    | ❔                                 | ❔                                 | --      |


</div></details>

## ⚛️ Web और छोटे कार्यक्रम मॉडल समर्थन सूची

<div id="fastdeploy-web-models"></div>

<details open><summary><b>Web और मिनी प्रोग्राम परिनियोजन समर्थन सूची (ढहने के लिए क्लिक करें)</b></summary><div>

| कार्य परिदृश्य              | नमूना                                                                                          | [web_demo](./../../examples/application/js/web_demo) |
|:------------------:|:-------------------------------------------------------------------------------------------:|:--------------------------------------------:|
| ---                | ---                                                                                         | [Paddle.js](./../../examples/application/js)         |
| Detection          | [FaceDetection](./../../examples/application/js/web_demo/src/pages/cv/detection)                    | ✅                                            |
| Detection          | [ScrewDetection](./../../examples/application/js/web_demo/src/pages/cv/detection)                   | ✅                                            |
| Segmentation       | [PaddleSeg/HumanSeg](./../../examples/application/js/web_demo/src/pages/cv/segmentation/HumanSeg) | ✅                                            |
| Object Recognition | [GestureRecognition](./../../examples/application/js/web_demo/src/pages/cv/recognition)             | ✅                                            |
| Object Recognition | [ItemIdentification](./../../examples/application/js/web_demo/src/pages/cv/recognition)             | ✅                                            |
| OCR                | [PaddleOCR/PP-OCRv3](./../../examples/application/js/web_demo/src/pages/cv/ocr)                   | ✅                                            |

</div></details>


## 💐 Acknowledge

यह परियोजना SDK पीढ़ी और डाउनलोड हम [EasyEdge](https://ai.baidu.com/easyedge/app/openSource) में मुक्त और खुली क्षमताओं का उपयोग करने के लिए आभारी हैं।

## ©️ License

<div id="fastdeploy-license"></div>

FastDeploy निम्नानुसार है [Apache-2.0 खुला स्रोत लाइसेंस](./../../LICENSE)
