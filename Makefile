# Makefile for SCRFD Standalone Implementation

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++11 -Wall -O2

# Paths (adjust these according to your system)
OPENCV_CFLAGS = $(shell pkg-config --cflags opencv4 2>/dev/null || pkg-config --cflags opencv)
OPENCV_LIBS = $(shell pkg-config --libs opencv4 2>/dev/null || pkg-config --libs opencv)

# RKNN SDK path (adjust according to your installation)
RKNN_SDK_PATH ?= /path/to/rknn/sdk
RKNN_INCLUDE = -I$(RKNN_SDK_PATH)/include
RKNN_LIBS = -L$(RKNN_SDK_PATH)/lib -lrknn_api

# Include and library flags
INCLUDES = $(OPENCV_CFLAGS) $(RKNN_INCLUDE)
LIBS = $(OPENCV_LIBS) $(RKNN_LIBS)

# Source files
SCRFD_SOURCES = scrfd.cpp
EXAMPLE_SOURCES = example.cpp

# Object files
SCRFD_OBJECTS = $(SCRFD_SOURCES:.cpp=.o)
EXAMPLE_OBJECTS = $(EXAMPLE_SOURCES:.cpp=.o)

# Targets
TARGET_LIB = libscrfd.a
TARGET_EXAMPLE = scrfd_example

# Default target
all: $(TARGET_LIB) $(TARGET_EXAMPLE)

# Build static library
$(TARGET_LIB): $(SCRFD_OBJECTS)
	ar rcs $@ $^
	@echo "Static library $(TARGET_LIB) created successfully"

# Build example executable
$(TARGET_EXAMPLE): $(EXAMPLE_OBJECTS) $(TARGET_LIB)
	$(CXX) $(CXXFLAGS) -o $@ $^ $(LIBS)
	@echo "Example executable $(TARGET_EXAMPLE) created successfully"

# Compile source files
%.o: %.cpp scrfd.h
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Clean build files
clean:
	rm -f $(SCRFD_OBJECTS) $(EXAMPLE_OBJECTS) $(TARGET_LIB) $(TARGET_EXAMPLE)
	@echo "Clean completed"

# Install (optional)
install: $(TARGET_LIB)
	mkdir -p /usr/local/lib
	mkdir -p /usr/local/include
	cp $(TARGET_LIB) /usr/local/lib/
	cp scrfd.h /usr/local/include/
	@echo "Installation completed"

# Uninstall (optional)
uninstall:
	rm -f /usr/local/lib/$(TARGET_LIB)
	rm -f /usr/local/include/scrfd.h
	@echo "Uninstallation completed"

# Help
help:
	@echo "Available targets:"
	@echo "  all       - Build library and example (default)"
	@echo "  libscrfd.a - Build static library only"
	@echo "  scrfd_example - Build example executable only"
	@echo "  clean     - Remove build files"
	@echo "  install   - Install library and header"
	@echo "  uninstall - Remove installed files"
	@echo "  help      - Show this help message"
	@echo ""
	@echo "Environment variables:"
	@echo "  RKNN_SDK_PATH - Path to RKNN SDK (default: /path/to/rknn/sdk)"
	@echo ""
	@echo "Example usage:"
	@echo "  make RKNN_SDK_PATH=/opt/rknn/sdk"
	@echo "  ./scrfd_example model.rknn image.jpg"

.PHONY: all clean install uninstall help
