# Visaulize(可视化)

## fastdeploy.vision.vis_detection

```{eval-rst}
.. autoclass:: fastdeploy.vision.vis_detection
    :members:
    :inherited-members:
```

## fastdeploy.vision.vis_segmentation

```{eval-rst}
.. autoclass:: fastdeploy.vision.vis_segmentation
    :members:
    :inherited-members:
```

## fastdeploy.vision.vis_keypoint_detection

```{eval-rst}
.. autoclass:: fastdeploy.vision.vis_keypoint_detection
    :members:
    :inherited-members:
```
## fastdeploy.vision.vis_face_detection

```{eval-rst}
.. autoclass:: fastdeploy.vision.vis_face_detection
    :members:
    :inherited-members:
```


## fastdeploy.vision.vis_face_alignment

```{eval-rst}
.. autoclass:: fastdeploy.vision.vis_face_alignment
    :members:
    :inherited-members:
```

## fastdeploy.vision.vis_matting

```{eval-rst}
.. autoclass:: fastdeploy.vision.vis_matting
    :members:
    :inherited-members:
```

## fastdeploy.vision.vis_ppocr

```{eval-rst}
.. autoclass:: fastdeploy.vision.vis_ppocr
    :members:
    :inherited-members:
```
