name: "ernie_seqcls"
platform: "ensemble"
max_batch_size: 64
input [
  {
    name: "INPUT"
    data_type: TYPE_STRING
    dims: [ 1 ]
  }
]
output [
  {
    name: "label"
    data_type: TYPE_INT64
    dims: [ 1 ]
  },
  {
    name: "confidence"
    data_type: TYPE_FP32
    dims: [ 1 ]
  }
]
ensemble_scheduling {
  step [
    {
      model_name: "ernie_tokenizer"
      model_version: 1
      input_map {
        key: "INPUT_0"
        value: "INPUT"
      }
      output_map {
        key: "OUTPUT_0"
        value: "tokenizer_input_ids"
      }
      output_map {
        key: "OUTPUT_1"
        value: "tokenizer_token_type_ids"
      }
    },
    {
      model_name: "ernie_seqcls_model"
      model_version: 1
      input_map {
        key: "input_ids"
        value: "tokenizer_input_ids"
      }
      input_map {
        key: "token_type_ids"
        value: "tokenizer_token_type_ids"
      }
      output_map {
        key: "linear_113.tmp_1"
        value: "OUTPUT_2"
      }
    },
    {
      model_name: "ernie_seqcls_postprocess"
      model_version: 1
      input_map {
        key: "POST_INPUT"
        value: "OUTPUT_2"
      }
      output_map {
        key: "POST_label"
        value: "label"
      }
      output_map {
        key: "POST_confidence"
        value: "confidence"
      }
    }
  ]
}

