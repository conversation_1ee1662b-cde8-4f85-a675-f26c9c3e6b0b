backend: "fastdeploy"
max_batch_size: 64
input [
    {
      name: "input_ids"
      data_type: TYPE_INT64
      dims: [ -1 ]
    },
    {
      name: "token_type_ids"
      data_type: TYPE_INT64
      dims: [ -1 ]
    }
]
output [
    {
      name: "linear_113.tmp_1"
      data_type: TYPE_FP32
      dims: [ -1, 7 ]
    }
]

instance_group [
  {
      # 创建1个实例
      count: 1
      # 使用GPU推理(KIND_CPU、KIND_GPU)
      kind: KIND_GPU
  }
]

optimization {
  execution_accelerators {
    gpu_execution_accelerator : [
      {
        name: "paddle"
      }
    ]
  }
}
