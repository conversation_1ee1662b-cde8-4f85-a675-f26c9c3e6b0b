[English](../../en/build_and_install/a311d.md) | 简体中文

# 晶晨 A311D 部署环境编译安装

## 导航目录

* [简介以及编译选项](#简介以及编译选项)
* [交叉编译环境搭建](#交叉编译环境搭建)
* [基于 Paddle Lite 的 FastDeploy 交叉编译库编译](#基于-paddle-lite-的-fastdeploy-交叉编译库编译)
* [准备设备运行环境](#准备设备运行环境)
* [基于 FastDeploy 在 A311D 上的部署示例](#基于-fastdeploy-在-a311d-上的部署示例)

## 简介以及编译选项

FastDeploy 基于 Paddle Lite 后端支持在晶晨 NPU 上进行部署推理。
更多详细的信息请参考：[Paddle Lite部署示例](https://www.paddlepaddle.org.cn/lite/develop/demo_guides/verisilicon_timvx.html)。

本文档介绍如何编译基于 Paddle Lite 的 C++ FastDeploy 交叉编译库。

相关编译选项说明如下：  
|编译选项|默认值|说明|备注|  
|:---|:---|:---|:---|  
|ENABLE_LITE_BACKEND|OFF|编译A311D部署库时需要设置为ON| - |
|WITH_TIMVX|OFF|编译A311D部署库时需要设置为ON| - |  
|TARGET_ABI|NONE|编译RK库时需要设置为arm64| - |

更多编译选项请参考[FastDeploy编译选项说明](./README.md)

## 交叉编译环境搭建

### 开发机环境需求  
- os：Ubuntu == 16.04
- cmake： version >= 3.10.0  

### 环境搭建
可以进入 FastDeploy/tools/timvx 目录，使用如下命令一键安装：
```bash
cd FastDeploy/tools/timvx
bash install.sh
```
也可以按照如下命令安装：
```bash
 # 1. Install basic software
apt update
apt-get install -y --no-install-recommends \
  gcc g++ git make wget python unzip

# 2. Install arm gcc toolchains
apt-get install -y --no-install-recommends \
  g++-arm-linux-gnueabi gcc-arm-linux-gnueabi \
  g++-arm-linux-gnueabihf gcc-arm-linux-gnueabihf \
  gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# 3. Install cmake 3.10 or above
wget -c https://mms-res.cdn.bcebos.com/cmake-3.10.3-Linux-x86_64.tar.gz && \
  tar xzf cmake-3.10.3-Linux-x86_64.tar.gz && \
  mv cmake-3.10.3-Linux-x86_64 /opt/cmake-3.10 && \
  ln -s /opt/cmake-3.10/bin/cmake /usr/bin/cmake && \
  ln -s /opt/cmake-3.10/bin/ccmake /usr/bin/ccmake
```

## 基于 Paddle Lite 的 FastDeploy 交叉编译库编译
搭建好交叉编译环境之后，编译命令如下：
```bash
# Download the latest source code
git clone https://github.com/PaddlePaddle/FastDeploy.git
cd FastDeploy  
mkdir build && cd build

# CMake configuration with A311D toolchain
cmake -DCMAKE_TOOLCHAIN_FILE=./../cmake/toolchain.cmake \
      -DWITH_TIMVX=ON  \
      -DTARGET_ABI=arm64 \
      -DENABLE_FLYCV=ON \ # 是否开启 FlyCV 优化前后处理，可以选择开启
      -DCMAKE_INSTALL_PREFIX=fastdeploy-timvx \
      -DENABLE_VISION=ON \ # 是否编译集成视觉模型的部署模块，可选择开启
      -Wno-dev ..

# Build FastDeploy A311D C++ SDK
make -j8
make install
```  
编译完成之后，会生成 fastdeploy-timvx 目录，表示基于 Paddle Lite TIM-VX 的 FastDeploy 库编译完成。

## 准备设备运行环境
部署前要保证晶晨 Linux Kernel NPU 驱动 galcore.so 版本及所适用的芯片型号与依赖库保持一致，在部署前，请登录开发板，并通过命令行输入以下命令查询 NPU 驱动版本，晶晨建议的驱动版本为：6.4.4.3
```bash
dmesg | grep Galcore
```  

如果当前版本不符合上述，请用户仔细阅读以下内容，以保证底层 NPU 驱动环境正确。

有两种方式可以修改当前的 NPU 驱动版本：
1. 手动替换 NPU 驱动版本。（推荐）
2. 刷机，刷取 NPU 驱动版本符合要求的固件。

### 手动替换 NPU 驱动版本
1. 使用如下命令下载解压 Paddle Lite demo，其中提供了现成的驱动文件
```bash
wget https://paddlelite-demo.bj.bcebos.com/devices/generic/PaddleLite-generic-demo.tar.gz
tar -xf PaddleLite-generic-demo.tar.gz
```
2. 使用 `uname -a` 查看 `Linux Kernel` 版本，确定为 `Linux` 系统 4.19.111 版本，
3. 将 `PaddleLite-generic-demo/libs/PaddleLite/linux/arm64/lib/verisilicon_timvx/viv_sdk_6_4_4_3/lib/a311d/4.9.113` 路径下的 `galcore.ko` 上传至开发板。

4. 登录开发板，命令行输入 `sudo rmmod galcore` 来卸载原始驱动，输入 `sudo insmod galcore.ko` 来加载传上设备的驱动。（是否需要 sudo 根据开发板实际情况，部分 adb 链接的设备请提前 adb root）。此步骤如果操作失败，请跳转至方法 2。
5. 在开发板中输入 `dmesg | grep Galcore` 查询 NPU 驱动版本，确定为：6.4.4.3

### 刷机
根据具体的开发板型号，向开发板卖家或官网客服索要 6.4.4.3 版本 NPU 驱动对应的固件和刷机方法。

更多细节请参考：[Paddle Lite准备设备环境](https://www.paddlepaddle.org.cn/lite/develop/demo_guides/verisilicon_timvx.html#zhunbeishebeihuanjing)

## 基于 FastDeploy 在 A311D 上的部署示例
1. A311D 上部署 PaddleClas 分类模型请参考：[PaddleClas 分类模型在 A311D 上的 C++ 部署示例](../../../examples/vision/classification/paddleclas/a311d/README.md)

2. A311D 上部署 PPYOLOE 检测模型请参考：[PPYOLOE 检测模型在 A311D 上的 C++ 部署示例](../../../examples/vision/detection/paddledetection/a311d/README.md)

3. A311D 上部署 YOLOv5 检测模型请参考：[YOLOv5 检测模型在 A311D 上的 C++ 部署示例](../../../examples/vision/detection/yolov5/a311d/README.md)

4. A311D 上部署 PP-LiteSeg 分割模型请参考：[PP-LiteSeg 分割模型在 A311D 上的 C++ 部署示例](../../../examples/vision/segmentation/paddleseg/a311d/README.md)
