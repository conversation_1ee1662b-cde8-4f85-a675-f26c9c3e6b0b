<view>
   <view class="img-view">
        <text class="title">点击图片进行预测</text>
        <scroll-view class="imgWrapper" scroll-x="true">
            <image
                class="img {{selectedIndex == index ? 'selected' : ''}}"
                wx:for="{{imgList}}"
                wx:key="index"
                src="{{item}}"
                mode="aspectFit"
                bindtap="selectImage"
                data-index="{{index}}"
            ></image>
            <canvas
                canvas-id="myCanvas"
                style="width: 960px; height: 960px; position: absolute; z-index: -1"
            ></canvas>
            <canvas
                canvas-id="result"
                style="width: 960px; height: 960px;"
            ></canvas>
        </scroll-view>
        <text class="result" wx:if="{{result}}" style="height: 300rpx;">文本框选坐标：{{result}}</text>
    </view>
</view>

<view class="mask" wx:if="{{!loaded}}">
    <text class="loading">loading…</text>
</view>
