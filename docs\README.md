[简体中文](README_CN.md)| English

# Tutorials

## Install

- [Install FastDeploy Prebuilt Libraries](en/build_and_install/download_prebuilt_libraries.md)
- [Build and Install FastDeploy Library on GPU Platform](en/build_and_install/gpu.md)
- [Build and Install FastDeploy Library on CPU Platform](en/build_and_install/cpu.md)
- [Build and Install FastDeploy Library on IPU Platform](en/build_and_install/ipu.md)
- [Build and Install FastDeploy Library on KunlunXin XPU Platform](en/build_and_install/kunlunxin.md)
- [Build and Install on RV1126 Platform](en/build_and_install/rv1126.md)
- [Build and Install on RK3588 and RK356X Platform](en/build_and_install/rknpu2.md)
- [Build and Install on A311D Platform](en/build_and_install/a311d.md)
- [Build and Install FastDeploy Library on  Nvidia Jetson Platform](en/build_and_install/jetson.md)
- [Build and Install FastDeploy Library on Android Platform](en/build_and_install/android.md)
- [Build and Install FastDeploy Serving Deployment Image](../serving/docs/EN/compile-en.md)

## A Quick Start - Demos

- [Python Deployment Demo](en/quick_start/models/python.md)
- [C++ Deployment Demo](en/quick_start/models/cpp.md)
- [A Quick Start on Runtime Python](en/quick_start/runtime/python.md)
- [A Quick Start on Runtime C++](en/quick_start/runtime/cpp.md)

## API

- [Python API](https://baidu-paddle.github.io/fastdeploy-api/python/html/)
- [C++ API](https://baidu-paddle.github.io/fastdeploy-api/cpp/html/)
- [Android Java API](../java/android)

## Performance Optimization

- [Quantization Acceleration](en/quantize.md)

## Frequent Q&As

- [1. How to Change Inference Backends](en/faq/how_to_change_backend.md)
- [2. How to Use FastDeploy C++ SDK on Windows Platform](en/faq/use_sdk_on_windows.md)
- [3. How to Use FastDeploy C++ SDK on Android Platform](en/faq/use_cpp_sdk_on_android.md)
- [4. Tricks of TensorRT](en/faq/tensorrt_tricks.md)
- [5. How to Develop a New Model](en/faq/develop_a_new_model.md)
- [6. How to Develop C API for a New Model](en/faq/develop_c_api_for_a_new_model.md)
- [7. How to Develop C# API for a New Model](en/faq/develop_c_sharp_api_for_a_new_model.md)

## More FastDeploy Deployment Module

- [Deployment AI Model as a Service](../serving)
- [Benchmark Testing](../benchmark)
