if(NOT ${ENABLE_LITE_BACKEND})
  set(ENABLE_LITE_BACKEND ON)
endif()

if(NOT CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
  if (NOT BUILD_FASTDEPLOY_PYTHON)
    message(STATUS "Build FastDeploy Ascend C++ library on X86 platform.")
    if(NOT PADDLELITE_URL)
      set(PADDLELITE_URL "https://paddle-qa.bj.bcebos.com/Paddle-Lite/DevelopDailyBuild/FastDeploy.CPP.inference_lite_lib.ubuntu.x86.huawei_ascend_npu.CANN5.1.RC2.alpha001.tar.gz")
    endif()
  else ()
    message(STATUS "Build FastDeploy Ascend Python library on X86 platform.")
    if(NOT PADDLELITE_URL)
      set(PADDLELITE_URL "https://paddle-qa.bj.bcebos.com/Paddle-Lite/DevelopDailyBuild/FastDeploy.Python.inference_lite_lib.ubuntu.x86.huawei_ascend_npu.CANN5.1.RC2.alpha001.tar.gz") 
    endif()
  endif()	
endif()


if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
  if (NOT BUILD_FASTDEPLOY_PYTHON)
    message(STATUS "Build FastDeploy Ascend C++ library on aarch64 platform.")
    if(NOT PADDLELITE_URL)
      set(PADDLELITE_URL "https://paddle-qa.bj.bcebos.com/Paddle-Lite/DevelopDailyBuild/FastDeploy.CPP.inference_lite_lib.ubuntu.armv8.huawei_ascend_npu.CANN5.1.RC2.alpha001.tar.gz")
    endif()
  else ()
    message(STATUS "Build FastDeploy Ascend Python library on aarch64 platform.")
    if(NOT PADDLELITE_URL)
      set(PADDLELITE_URL "https://paddle-qa.bj.bcebos.com/Paddle-Lite/DevelopDailyBuild/FastDeploy.Python.inference_lite_lib.ubuntu.armv8.huawei_ascend_npu.CANN5.1.RC2.alpha001.tar.gz")
    endif()
  endif()	
endif()
