#include "scrfd.h"
#include <opencv2/opencv.hpp>
#include <iostream>

int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cout << "Usage: " << argv[0] << " <model_path> <image_path>" << std::endl;
        return -1;
    }
    
    std::string model_path = argv[1];
    std::string image_path = argv[2];
    
    // Create SCRFD instance
    SCRFD detector(model_path);
    
    // Initialize the model
    if (!detector.Initialize()) {
        std::cerr << "Failed to initialize SCRFD model" << std::endl;
        return -1;
    }
    
    // Load image
    cv::Mat image = cv::imread(image_path);
    if (image.empty()) {
        std::cerr << "Failed to load image: " << image_path << std::endl;
        return -1;
    }
    
    // Perform face detection
    FaceDetectionResult result;
    bool success = detector.Predict(image, &result, 0.5f, 0.4f);
    
    if (!success) {
        std::cerr << "Failed to perform face detection" << std::endl;
        return -1;
    }
    
    // Print results
    std::cout << "Detected " << result.boxes.size() << " faces:" << std::endl;
    for (size_t i = 0; i < result.boxes.size(); ++i) {
        const auto& box = result.boxes[i];
        float score = result.scores[i];
        
        std::cout << "Face " << i << ": "
                  << "bbox=[" << box[0] << ", " << box[1] << ", " << box[2] << ", " << box[3] << "], "
                  << "score=" << score << std::endl;
        
        // Draw bounding box
        cv::rectangle(image, 
                     cv::Point(static_cast<int>(box[0]), static_cast<int>(box[1])),
                     cv::Point(static_cast<int>(box[2]), static_cast<int>(box[3])),
                     cv::Scalar(0, 255, 0), 2);
        
        // Draw landmarks if available
        if (result.landmarks_per_face > 0) {
            for (int j = 0; j < result.landmarks_per_face; ++j) {
                const auto& landmark = result.landmarks[i * result.landmarks_per_face + j];
                cv::circle(image, 
                          cv::Point(static_cast<int>(landmark[0]), static_cast<int>(landmark[1])),
                          2, cv::Scalar(0, 0, 255), -1);
            }
        }
        
        // Put score text
        std::string score_text = std::to_string(score).substr(0, 4);
        cv::putText(image, score_text,
                   cv::Point(static_cast<int>(box[0]), static_cast<int>(box[1]) - 5),
                   cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(0, 255, 0), 1);
    }
    
    // Save result image
    std::string output_path = "result_" + image_path;
    cv::imwrite(output_path, image);
    std::cout << "Result saved to: " << output_path << std::endl;
    
    return 0;
}
