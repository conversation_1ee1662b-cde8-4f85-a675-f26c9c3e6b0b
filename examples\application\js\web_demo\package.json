{"name": "paddle-js-demo", "version": "1.0.0", "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@antv/x6": "^1.34.2", "@paddle-js-models/detect": "3.0.1", "@paddle-js-models/facedetect": "3.0.1", "@paddle-js-models/gesture": "3.0.1", "@paddle-js-models/humanseg": "3.0.1", "@paddle-js-models/humanseg_gpu": "3.0.1", "@paddle-js-models/mobilenet": "3.0.1", "@paddle-js-models/ocr": "4.0.0", "@paddle-js-models/ocrdet": "4.0.1", "@paddlejs/paddlejs-backend-webgl": "^1.2.9", "@paddlejs/paddlejs-core": "^2.2.0", "element-plus": "^2.2.16", "less": "^4.1.3", "less-loader": "^11.0.0", "pinia": "^2.0.21", "vue": "^3.2.38", "vue-router": "^4.1.5"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@rushstack/eslint-patch": "^1.1.4", "@types/node": "^16.11.56", "@vitejs/plugin-vue": "^3.0.3", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "commitlint": "^17.1.2", "eslint": "8.5.0", "eslint-plugin-vue": "^9.3.0", "husky": "^8.0.1", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "typescript": "~4.7.4", "unplugin-auto-import": "^0.11.2", "unplugin-vue-components": "^0.22.4", "vite": "^3.0.9", "vue-tsc": "^0.40.7"}}