cmake_minimum_required(VERSION 3.10)
project(SCRFD_Standalone)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(OpenCV REQUIRED)

# RKNN library path (adjust according to your RKNN SDK installation)
set(RKNN_SDK_PATH "/path/to/rknn/sdk" CACHE PATH "Path to RKNN SDK")
set(RKNN_INCLUDE_DIR "${RKNN_SDK_PATH}/include")
set(RKNN_LIB_DIR "${RKNN_SDK_PATH}/lib")

# Check if RKNN SDK exists
if(NOT EXISTS ${RKNN_INCLUDE_DIR}/rknn_api.h)
    message(FATAL_ERROR "RKNN SDK not found. Please set RKNN_SDK_PATH to the correct path.")
endif()

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${RKN<PERSON>_INCLUDE_DIR})

# Find RKNN library
find_library(RKNN_LIB
    NAMES rknn_api librknn_api
    PATHS ${RKNN_LIB_DIR}
    NO_DEFAULT_PATH
)

if(NOT RKNN_LIB)
    message(FATAL_ERROR "RKNN library not found in ${RKNN_LIB_DIR}")
endif()

# Create SCRFD library
add_library(scrfd STATIC scrfd.cpp)
target_link_libraries(scrfd ${OpenCV_LIBS} ${RKNN_LIB})

# Create example executable
add_executable(scrfd_example example.cpp)
target_link_libraries(scrfd_example scrfd ${OpenCV_LIBS} ${RKNN_LIB})

# Print information
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
message(STATUS "RKNN SDK path: ${RKNN_SDK_PATH}")
message(STATUS "RKNN library: ${RKNN_LIB}")
