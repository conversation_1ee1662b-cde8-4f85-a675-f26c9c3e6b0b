# Semantic Segmentation(语义分割)


## fastdeploy.vision.segmentation.PaddleSegPreprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.segmentation.PaddleSegPreprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.segmentation.PaddleSegModel

```{eval-rst}
.. autoclass:: fastdeploy.vision.segmentation.PaddleSegModel
    :members:
    :inherited-members:
```

## fastdeploy.vision.segmentation.PaddleSegPostprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.segmentation.PaddleSegPostprocessor
    :members:
    :inherited-members:
```
