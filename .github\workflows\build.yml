name: Build
on: [pull_request]

jobs:
  macOS-latest-py:
    runs-on: macos-latest

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v1

      - name: Get CMake
        uses: lukka/get-cmake@latest

      - name: Get Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Build FastDeploy
        working-directory: ./python
        run: |
          export ENABLE_ORT_BACKEND=ON
          export ENABLE_PADDLE_BACKEND=OFF
          export ENABLE_OPENVINO_BACKEND=OFF
          export ENABLE_VISION=ON
          export ENABLE_TEXT=ON
          python -m pip install wheel
          python setup.py build
          python setup.py bdist_wheel
          ls -l
