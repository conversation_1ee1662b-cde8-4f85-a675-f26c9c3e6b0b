# Object Detection(目标检测)

## fastdeploy.vision.detection.PaddleDetPreprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.PaddleDetPreprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.PaddleDetPostprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.PaddleDetPostprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.PPYOLOE

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.PPYOLOE
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.PPYOLO

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.PPYOLO
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.PicoDet

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.PicoDet
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.PaddleYOLOX

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.PaddleYOLOX
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv3

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.PaddleYOLOX
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.FasterRCNN

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.FasterRCNN
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.MaskRCNN

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.MaskRCNN
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.NanoDetPlus

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.NanoDetPlus
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.ScaledYOLOv4

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.ScaledYOLOv4
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOR

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOR
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv5Preprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv5Preprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv5Postprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv5Postprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv5

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv5
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv5Lite

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv5Lite
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv6

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv6
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv7Preprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv7Preprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv7Postprocessor

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv7Postprocessor
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv7

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv7
    :members:
    :inherited-members:
```


## fastdeploy.vision.detection.YOLOR

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOR
    :members:
    :inherited-members:
```


## fastdeploy.vision.detection.YOLOv7End2EndORT

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv7End2EndORT
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOv7End2EndTRT

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOv7End2EndTRT
    :members:
    :inherited-members:
```

## fastdeploy.vision.detection.YOLOX

```{eval-rst}
.. autoclass:: fastdeploy.vision.detection.YOLOX
    :members:
    :inherited-members:
```
